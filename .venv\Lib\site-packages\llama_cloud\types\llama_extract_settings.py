# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .chunk_mode import ChunkMode
from .llama_parse_parameters import LlamaParseParameters
from .multimodal_parse_resolution import MultimodalParseResolution
from .struct_parse_conf import StructParseConf

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class LlamaExtractSettings(pydantic.BaseModel):
    """
    All settings for the extraction agent. Only the settings in ExtractConfig
    are exposed to the user.
    """

    max_file_size: typing.Optional[int] = pydantic.Field(
        description="The maximum file size (in bytes) allowed for the document."
    )
    max_file_size_ui: typing.Optional[int] = pydantic.Field(
        description="The maximum file size (in bytes) allowed for the document."
    )
    max_pages: typing.Optional[int] = pydantic.Field(
        description="The maximum number of pages allowed for the document."
    )
    chunk_mode: typing.Optional[ChunkMode] = pydantic.Field(description="The mode to use for chunking the document.")
    max_chunk_size: typing.Optional[int] = pydantic.Field(
        description="The maximum size of the chunks (in tokens) to use for chunking the document."
    )
    extraction_agent_config: typing.Optional[typing.Dict[str, StructParseConf]] = pydantic.Field(
        description="The configuration for the extraction agent."
    )
    use_multimodal_parsing: typing.Optional[bool] = pydantic.Field(
        description="Whether to use experimental multimodal parsing."
    )
    use_pixel_extraction: typing.Optional[bool] = pydantic.Field(
        description="Whether to use extraction over pixels for multimodal mode."
    )
    llama_parse_params: typing.Optional[LlamaParseParameters] = pydantic.Field(
        description="LlamaParse related settings."
    )
    multimodal_parse_resolution: typing.Optional[MultimodalParseResolution] = pydantic.Field(
        description="The resolution to use for multimodal parsing."
    )

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
