# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class QuotaConfigurationConfigurationType(str, enum.Enum):
    """
    The quota configuration type
    """

    RATE_LIMIT_PARSE_CONCURRENT_PREMIUM = "rate_limit_parse_concurrent_premium"
    RATE_LIMIT_PARSE_CONCURRENT_DEFAULT = "rate_limit_parse_concurrent_default"
    RATE_LIMIT_CONCURRENT_JOBS_IN_EXECUTION_DEFAULT = "rate_limit_concurrent_jobs_in_execution_default"
    RATE_LIMIT_CONCURRENT_JOBS_IN_EXECUTION_DOC_INGEST = "rate_limit_concurrent_jobs_in_execution_doc_ingest"

    def visit(
        self,
        rate_limit_parse_concurrent_premium: typing.Callable[[], T_Result],
        rate_limit_parse_concurrent_default: typing.Callable[[], T_Result],
        rate_limit_concurrent_jobs_in_execution_default: typing.Callable[[], T_Result],
        rate_limit_concurrent_jobs_in_execution_doc_ingest: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is QuotaConfigurationConfigurationType.RATE_LIMIT_PARSE_CONCURRENT_PREMIUM:
            return rate_limit_parse_concurrent_premium()
        if self is QuotaConfigurationConfigurationType.RATE_LIMIT_PARSE_CONCURRENT_DEFAULT:
            return rate_limit_parse_concurrent_default()
        if self is QuotaConfigurationConfigurationType.RATE_LIMIT_CONCURRENT_JOBS_IN_EXECUTION_DEFAULT:
            return rate_limit_concurrent_jobs_in_execution_default()
        if self is QuotaConfigurationConfigurationType.RATE_LIMIT_CONCURRENT_JOBS_IN_EXECUTION_DOC_INGEST:
            return rate_limit_concurrent_jobs_in_execution_doc_ingest()
