# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .document_chunk_mode import DocumentChunkMode
from .extract_config_priority import ExtractConfigPriority
from .extract_mode import ExtractMode
from .extract_target import ExtractTarget

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class ExtractConfig(pydantic.BaseModel):
    """
    Additional parameters for the extraction agent.
    """

    priority: typing.Optional[ExtractConfigPriority]
    extraction_target: typing.Optional[ExtractTarget] = pydantic.Field(description="The extraction target specified.")
    extraction_mode: typing.Optional[ExtractMode] = pydantic.Field(description="The extraction mode specified.")
    multimodal_fast_mode: typing.Optional[bool] = pydantic.Field(
        description="Whether to use fast mode for multimodal extraction."
    )
    system_prompt: typing.Optional[str]
    use_reasoning: typing.Optional[bool] = pydantic.Field(description="Whether to use reasoning for the extraction.")
    cite_sources: typing.Optional[bool] = pydantic.Field(description="Whether to cite sources for the extraction.")
    confidence_scores: typing.Optional[bool] = pydantic.Field(
        description="Whether to fetch confidence scores for the extraction."
    )
    chunk_mode: typing.Optional[DocumentChunkMode] = pydantic.Field(
        description="The mode to use for chunking the document."
    )
    high_resolution_mode: typing.Optional[bool] = pydantic.Field(
        description="Whether to use high resolution mode for the extraction."
    )
    invalidate_cache: typing.Optional[bool] = pydantic.Field(
        description="Whether to invalidate the cache for the extraction."
    )
    page_range: typing.Optional[str]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
