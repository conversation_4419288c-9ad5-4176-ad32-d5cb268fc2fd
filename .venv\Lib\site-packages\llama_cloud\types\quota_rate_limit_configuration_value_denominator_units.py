# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class QuotaRateLimitConfigurationValueDenominatorUnits(str, enum.Enum):
    SECOND = "second"
    MINUTE = "minute"
    HOUR = "hour"
    DAY = "day"

    def visit(
        self,
        second: typing.Callable[[], T_Result],
        minute: typing.Callable[[], T_Result],
        hour: typing.Callable[[], T_Result],
        day: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is QuotaRateLimitConfigurationValueDenominatorUnits.SECOND:
            return second()
        if self is QuotaRateLimitConfigurationValueDenominatorUnits.MINUTE:
            return minute()
        if self is QuotaRateLimitConfigurationValueDenominatorUnits.HOUR:
            return hour()
        if self is QuotaRateLimitConfigurationValueDenominatorUnits.DAY:
            return day()
