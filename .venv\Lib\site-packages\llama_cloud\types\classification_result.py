# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class ClassificationResult(pydantic.BaseModel):
    """
    Result of classifying a single file.

    Contains the classification outcome with confidence score and matched rule info.
    """

    file_id: str = pydantic.Field(description="The ID of the classified file")
    type: str = pydantic.Field(description="The assigned document type ('unknown' if no rules matched)")
    confidence: float = pydantic.Field(description="Confidence score of the classification (0.0-1.0)")
    matched_rule: typing.Optional[str]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
