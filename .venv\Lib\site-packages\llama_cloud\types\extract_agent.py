# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

import typing_extensions

from ..core.datetime_utils import serialize_datetime
from .extract_agent_data_schema_value import ExtractAgentDataSchemaValue
from .extract_config import ExtractConfig

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class ExtractAgent(pydantic.BaseModel):
    """
    Schema and configuration for creating an extraction agent.
    """

    id: str = pydantic.Field(description="The id of the extraction agent.")
    name: str = pydantic.Field(description="The name of the extraction agent.")
    project_id: str = pydantic.Field(description="The ID of the project that the extraction agent belongs to.")
    data_schema: typing.Dict[str, typing.Optional[ExtractAgentDataSchemaValue]] = pydantic.Field(
        description="The schema of the data."
    )
    config: ExtractConfig = pydantic.Field(description="The configuration parameters for the extraction agent.")
    custom_configuration: typing.Optional[typing_extensions.Literal["default"]]
    created_at: typing.Optional[dt.datetime]
    updated_at: typing.Optional[dt.datetime]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
