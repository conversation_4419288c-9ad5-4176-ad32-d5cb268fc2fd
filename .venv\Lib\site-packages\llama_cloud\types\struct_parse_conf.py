# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .extract_models import ExtractModels
from .prompt_conf import PromptConf
from .schema_relax_mode import SchemaRelaxMode
from .struct_mode import StructMode

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class StructParseConf(pydantic.BaseModel):
    """
    Configuration for the structured parsing agent.
    """

    model: typing.Optional[ExtractModels] = pydantic.Field(description="The model to use for the structured parsing.")
    temperature: typing.Optional[float] = pydantic.Field(
        description="The temperature to use for the structured parsing."
    )
    relaxation_mode: typing.Optional[SchemaRelaxMode] = pydantic.Field(
        description="The relaxation mode to use for the structured parsing."
    )
    struct_mode: typing.Optional[StructMode] = pydantic.Field(
        description="The struct mode to use for the structured parsing."
    )
    fetch_logprobs: typing.Optional[bool] = pydantic.Field(
        description="Whether to fetch logprobs for the structured parsing."
    )
    handle_missing: typing.Optional[bool] = pydantic.Field(
        description="Whether to handle missing fields in the schema."
    )
    use_reasoning: typing.Optional[bool] = pydantic.Field(
        description="Whether to use reasoning for the structured extraction."
    )
    cite_sources: typing.Optional[bool] = pydantic.Field(
        description="Whether to cite sources for the structured extraction."
    )
    prompt_conf: typing.Optional[PromptConf] = pydantic.Field(
        description="The prompt configuration for the structured parsing."
    )

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
