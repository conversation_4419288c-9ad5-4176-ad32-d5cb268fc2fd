# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class DataSourceReaderVersionMetadataReaderVersion(str, enum.Enum):
    ONE_0 = "1.0"
    TWO_0 = "2.0"

    def visit(self, one_0: typing.Callable[[], T_Result], two_0: typing.Callable[[], T_Result]) -> T_Result:
        if self is DataSourceReaderVersionMetadataReaderVersion.ONE_0:
            return one_0()
        if self is DataSourceReaderVersionMetadataReaderVersion.TWO_0:
            return two_0()
