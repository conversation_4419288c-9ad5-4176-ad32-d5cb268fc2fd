{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 中文 RAG：Semantic Chunker + SQLite + Chroma + jieba BM25（Jupyter Notebook）\n", "\n", "本 Notebook 演示：\n", "- **分块**：使用 LlamaIndex 的 **SemanticSplitterNodeParser（semantic chunker）** 进行语义分块；\n", "- **存储**：使用 **SQLite** 持久化原始节点（文本+元数据），使用 **Chroma** 持久化向量；\n", "- **检索**：使用 **jieba** 对 **用户 query 先分词**，再做 **BM25** 检索；同时输出 **BM25** 与 **向量检索** 的命中文本块与得分。\n", "\n", "> 运行前准备：把你的中文 `.txt` 或 `.md` 文本放到 `./data/` 目录；首次运行可自动创建一个示例文件。\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 25.1.1 -> 25.2\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["# 如果本地尚未安装相关依赖，请先执行本单元（需要网络）\n", "%pip install -q \"llama-index>=0.10\" llama-index-vector-stores-chroma chromadb jieba bm25s \"llama-index-embeddings-huggingface>=0.2.0\" pandas\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Desktop\\ai_mindmap\\.venv\\Lib\\site-packages\\jieba\\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.\n", "  import pkg_resources\n"]}, {"name": "stdout", "output_type": "stream", "text": ["resource module not available on Windows\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c568e0b43ff6406dbfa57553b492932c", "version_major": 2, "version_minor": 0}, "text/plain": ["modules.json:   0%|          | 0.00/349 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "48486d1ea3ca4bc9ab201e89d58d00ad", "version_major": 2, "version_minor": 0}, "text/plain": ["config_sentence_transformers.json:   0%|          | 0.00/124 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "afe1d1a3fa994ff4b2b689ab1c4ed57a", "version_major": 2, "version_minor": 0}, "text/plain": ["README.md: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cce1a6237ca94b15a4c90979eb1c1fb6", "version_major": 2, "version_minor": 0}, "text/plain": ["sentence_bert_config.json:   0%|          | 0.00/52.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b239d0abf3154b4396d50e067db9fd3a", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/776 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8bf3630aa33c48358bf83f722c5bad09", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/95.8M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b883e59000f944d79f952157d1092184", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/367 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f75b366860284e5794d17976c7edca20", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.txt: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "55add56c1f51402fb79f9864c52fcaaf", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e231a0c11961468d8bf32cd0e5363474", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/125 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a138ecfb7c82402b97d626e9d368daa2", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pathlib import Path\n", "import os, json, sqlite3\n", "from typing import List, <PERSON><PERSON>\n", "\n", "import pandas as pd\n", "import jieba, bm25s\n", "\n", "from llama_index.core import Document, StorageContext, VectorStoreIndex, Settings\n", "from llama_index.core.node_parser import SemanticSplitterNodeParser\n", "from llama_index.core.schema import Node\n", "from llama_index.embeddings.huggingface import HuggingFaceEmbedding\n", "from llama_index.vector_stores.chroma import ChromaVectorStore\n", "import chromadb\n", "\n", "# 选择一个适合中文的开源向量模型，避免依赖云 API\n", "Settings.embed_model = HuggingFaceEmbedding(model_name=\"BAAI/bge-small-zh-v1.5\")\n", "\n", "DATA_DIR = Path(\"./data\"); DATA_DIR.mkdir(exist_ok=True)\n", "CHROMA_DIR = \"./chroma_db\"\n", "SQLITE_DB = \"./nodes.db\"\n", "TABLE = \"nodes\"\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def init_sqlite(conn: sqlite3.Connection):\n", "    cur = conn.cursor()\n", "    cur.execute(\n", "        f\"\"\"CREATE TABLE IF NOT EXISTS {TABLE}(\n", "                id TEXT PRIMARY KEY,\n", "                text TEXT NOT NULL,\n", "                metadata TEXT\n", "            );\"\"\"\n", "    )\n", "    conn.commit()\n", "\n", "def save_nodes_to_sqlite(conn: sqlite3.Connection, nodes: List[Node]):\n", "    cur = conn.cursor()\n", "    for n in nodes:\n", "        meta = n.metadata or {}\n", "        cur.execute(\n", "            f\"INSERT OR REPLACE INTO {TABLE}(id, text, metadata) VALUES (?, ?, ?);\",\n", "            (n.node_id, n.get_content(), json.dumps(meta, ensure_ascii=False)),\n", "        )\n", "    conn.commit()\n", "\n", "def load_nodes_from_sqlite(conn: sqlite3.Connection) -> List[Node]:\n", "    cur = conn.cursor()\n", "    rows = cur.execute(f\"SELECT id, text, metadata FROM {TABLE};\").fetchall()\n", "    out: List[Node] = []\n", "    for nid, text, meta in rows:\n", "        node = Node(text=text, id_=nid, metadata=json.loads(meta) if meta else {})\n", "        out.append(node)\n", "    return out\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def load_documents_from_dir(data_dir: Path) -> List[Document]:\n", "    docs: List[Document] = []\n", "    for p in data_dir.iterdir():\n", "        if p.is_file() and p.suffix.lower() in {\".txt\", \".md\"}:\n", "            docs.append(Document(text=p.read_text(encoding=\"utf-8\"), metadata={\"source\": p.name}))\n", "    return docs\n", "\n", "def semantic_chunk_documents(docs: List[Document]) -> List[Node]:\n", "    # 语义分块（semantic chunker）。可根据语料调整 buffer_size 或自定义句子切分。\n", "    splitter = SemanticSplitterNodeParser(\n", "        buffer_size=1,\n", "        embed_model=Settings.embed_model  # 使用全局设置的嵌入模型\n", "    )\n", "    return splitter.get_nodes_from_documents(docs)\n", "\n", "def build_or_load_chroma_index(nodes: List[Node]) -> VectorStoreIndex:\n", "    db = chromadb.PersistentClient(path=CHROMA_DIR)\n", "    collection = db.get_or_create_collection(\"dense_vectors\")\n", "    vector_store = ChromaVectorStore(chroma_collection=collection)\n", "    storage_context = StorageContext.from_defaults(vector_store=vector_store)\n", "    index = VectorStoreIndex(nodes=nodes, storage_context=storage_context)\n", "    return index\n", "\n", "def tokenize_zh(text: str):\n", "    return [t.strip() for t in jieba.lcut(text) if t.strip()]\n", "\n", "def build_bm25_from_nodes(nodes: List[Node]) -> bm25s.BM25:\n", "    corpus_tokens = [tokenize_zh(n.get_content()) for n in nodes]\n", "    bm25 = bm25s.BM25()\n", "    bm25.index(corpus_tokens, show_progress=False)\n", "    return bm25\n", "\n", "def bm25_search(bm25: bm25s.BM25, nodes: List[Node], query: str, top_k: int = 5):\n", "    q_tokens = tokenize_zh(query)\n", "    idxs_list, scores_list = bm25.retrieve([q_tokens], k=top_k, show_progress=False)\n", "    idxs, scores = idxs_list[0], scores_list[0]\n", "    results = []\n", "    for i, s in zip(idxs, scores):\n", "        results.append((nodes[int(i)], float(s)))\n", "    return results\n"]}, {"cell_type": "code", "execution_count": 12, "id": "0a3b2759", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📄 加载了 2 个文档\n", "\n", "🔍 语义分块结果：\n", "总共生成 9 个文档块\n", "============================================================\n", "\n", "📝 分块 1:\n", "字符数: 2495\n", "内容预览: # Python 第八章 函数\n", "\n", "在本章中，你将学习编写**函数**（function）。函数是带名字的代码块，用于完成具体的工作。要执行函数定义的特定任务，可**调用**（call）该函数。当需要在...\n", "元数据: {'source': 'python第八章.md'}\n", "----------------------------------------\n", "\n", "📝 分块 2:\n", "字符数: 820\n", "内容预览: describe_pet('hamster', 'harry')\n", "```\n", "\n", "这个函数的定义表明，它需要一个动物类型和一个名字。在调用 describe_pet()时，需要按顺序提供一个动物类型和一个名...\n", "元数据: {'source': 'python第八章.md'}\n", "----------------------------------------\n", "\n", "📝 分块 3:\n", "字符数: 14\n", "内容预览: I have a dog.\n", "...\n", "元数据: {'source': 'python第八章.md'}\n", "----------------------------------------\n", "\n", "📝 分块 4:\n", "字符数: 436\n", "内容预览: My dog's name is <PERSON>.\n", "```\n", "\n", "多次调用同一个函数是一种效率极高的工作方式。只需在函数中编写一次描述宠物的代码，每当需要描述新宠物时，就都可以调用这个函数并向它提供新宠物的...\n", "元数据: {'source': 'python第八章.md'}\n", "----------------------------------------\n", "\n", "📝 分块 5:\n", "字符数: 3172\n", "内容预览: describe_pet('harry', 'hamster')\n", "```\n", "\n", "在这个函数调用中，先指定名字，再指定动物类型。由于实参'harry'在前，这个值将被赋给形参 animal_type，而后面...\n", "元数据: {'source': 'python第八章.md'}\n", "----------------------------------------\n", "\n", "📝 分块 6:\n", "字符数: 4652\n", "内容预览: describe_pet()\n", "```\n", "\n", "#### 错误信息的解读\n", "\n", "Python 发现该函数调用缺少必要的信息，并用 traceback 指出了这一点：\n", "\n", "```\n", "Traceback (most re...\n", "元数据: {'source': 'python第八章.md'}\n", "----------------------------------------\n", "\n", "📝 分块 7:\n", "字符数: 1581\n", "内容预览: ```\n", "\n", "在这个示例中，使用的是 get_formatted_name()的简单版本，不涉及中间名。while 循环让用户输入姓名：提示用户依次输入名和姓。\n", "\n", "#### 提供退出条件\n", "\n", "但这个 whi...\n", "元数据: {'source': 'python第八章.md'}\n", "----------------------------------------\n", "\n", "📝 分块 8:\n", "字符数: 12012\n", "内容预览: Hello, Margot!\n", "```\n", "\n", "输出完全符合预期。每个用户都看到了一条个性化的问候语。每当需要问候一组用户时，都可调用这个函数。\n", "\n", "### 在函数中修改列表\n", "\n", "将列表传递给函数后，函数就可以对...\n", "元数据: {'source': 'python第八章.md'}\n", "----------------------------------------\n", "\n", "📝 分块 9:\n", "字符数: 2621\n", "内容预览: 人工智能：未来的奇迹？灾难？随便聊聊\n", "人工智能，这个词我第一次听说是在高中吧，那时候老师说未来会有机器人帮我们打扫卫生、帮我们写作业，我当时笑了，心想这哪可能？结果现在扫地机器人满大街跑，ChatGP...\n", "元数据: {'source': '人工智能.txt'}\n", "----------------------------------------\n"]}], "source": ["# 单独测试语义分块效果\n", "def analyze_semantic_chunking():\n", "    \"\"\"分析语义分块的详细结果\"\"\"\n", "    \n", "    # 加载文档\n", "    docs = load_documents_from_dir(Path(\"./data\"))\n", "    print(f\"📄 加载了 {len(docs)} 个文档\")\n", "    \n", "    # 创建语义分块器\n", "    splitter = SemanticSplitterNodeParser(\n", "        buffer_size=1,\n", "        breakpoint_percentile_threshold=80,  # 降低阈值，产生更多块\n", "        embed_model=Settings.embed_model\n", "    )\n", "    \n", "    # 执行分块\n", "    nodes = splitter.get_nodes_from_documents(docs)\n", "    \n", "    print(f\"\\n🔍 语义分块结果：\")\n", "    print(f\"总共生成 {len(nodes)} 个文档块\")\n", "    print(\"=\" * 60)\n", "    \n", "    # 输出每个分块的详细信息\n", "    for i, node in enumerate(nodes, 1):\n", "        print(f\"\\n📝 分块 {i}:\")\n", "        print(f\"字符数: {len(node.text)}\")\n", "        print(f\"内容预览: {node.text[:100]}...\")\n", "        if hasattr(node, 'metadata') and node.metadata:\n", "            print(f\"元数据: {node.metadata}\")\n", "        print(\"-\" * 40)\n", "    \n", "    return nodes\n", "\n", "# 执行分析\n", "chunked_nodes = analyze_semantic_chunking()"]}, {"cell_type": "code", "execution_count": 17, "id": "7a466e9e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📄 加载了 2 个文档\n", "============================================================\n", "\n", "📝 文档 1: python第八章.md\n", "原文字符数: 25182\n", "分词后词数: 11339\n", "分词结果预览 (前20个词): ['#', 'Python', '第八章', '函数', '在', '本章', '中', '，', '你', '将', '学习', '编写', '*', '*', '函数', '*', '*', '（', 'function', '）']\n", "高频词 (前10个):\n", "  '，': 475次\n", "  '的': 465次\n", "  '_': 398次\n", "  '`': 390次\n", "  ''': 317次\n", "  '。': 296次\n", "  '函数': 262次\n", "  '\"': 224次\n", "  '(': 219次\n", "  ')': 219次\n", "\n", "完整分词结果:\n", "# / Python / 第八章 / 函数 / 在 / 本章 / 中 / ， / 你 / 将 / 学习 / 编写 / * / * / 函数 / * / * / （ / function / ） / 。 / 函数 / 是 / 带 / 名字 / 的 / 代码 / 块 / ， / 用于 / 完成 / 具体 / 的 / 工作 / 。 / 要 / 执行 / 函数 / 定义 / 的 / 特定 / 任务 / ， / 可 / * / * / 调用 / * / * / （ / call / ） / 该 / 函数 / 。 / 当 / 需要 / 在 / 程序 / 中 / 多次 / 执行 / 同 / 一项 / 任务 / 时 / ， / 无须 / 反复 / 编写 / 完成 / 该 / 任务 / 的 / 代码 / ， / 只 / 需要 / 调用 / 执行 / 该 / 任务 / 的 / 函数 / ， / 让 / Python / 运行 / 其中 / 的 / 代码 / 即可 / 。 / 你 / 将 / 发现 / ， / 使用 / 函数 / ， / 程序 / 编写 / 、 / 阅读 / 、 / 测试 / 和 / 修复 / 起来 / 都 / 会 / 更 / 容易 / 。 / 你 / 还 / 将 / 学习 / 各种 / 向 / 函数 / 传递信息 / 的 / 方式 / ， / 学习 / 编写 / 主要 / 任务 / 是 / 显示信息 / 的 / 函数 / ， / 以及 / 用于 / 处理 / 数据 / 并 / 返回 / 一个 / 或 / 一组 / 值 / 的 / 函数 / 。 / 最后 / ， / 你 / 将 / 学习 / 如何 / 将 / 函数 / 存储 / 在 / 称为 / * / * / 模块 / * / * / （ / module / ） / 的 / 独立 / 文件 / 中 / ， / 让 / 主程序 / 文件 / 更加 / 整洁 / 。 / ## / 定义 / 函数 / 下面 / 是 / 一个 / 打印 / 问候语 / 的 / 简单 / 函数 / ， / 名为 / greet / _ / user / ( / ) / ： / ### / greeter / . / py / 示例 / ` / ` / ` / python / def / greet / _ / user / ( / ) / : / \" / \" / \" / 显示 / 简单 / 的 / 问候语 / \" / \" / \" / print / ( / \" / Hello / ! / \" / ) / greet / _ / user / ( / ) / ` / ` / ` / #### / 函数 / 定义 / 的 / 基本 / 结构 / 这个 / 示例 / 演示 / 了 / 最 / 简单 / 的 / 函数 / 结构 / 。 / 第一行 / 代码 / 使用 / 关键字 / def / 来 / 告诉 / Python / ， / 你 / 要 / 定义 / 一个 / 函数 / 。 / 这是 / * / * / 函数 / 定义 / * / * / ， / 向 / Python / 指出 / 了 / 函数 / 名 / ， / 还 / 可以 / 在 / 括号 / 内 / 指出 / 函数 / 为 / 完成 / 任务 / 需要 / 什么样 / 的 / 信息 / 。 / 在 / 这里 / ， / 函数 / 名为 / greet / _ / user / ( / ) / ， / 它 / 不 / 需要 / 任何 / 信息 / 就 / 能 / 完成 / 工作 / ， / 因此 / 括号 / 内 / 是 / 空 / 的 / （ / 即便如此 / ， / 括号 / 也 / 必不可少 / ） / 。 / 最后 / ， / 定义 / 以 / 冒号 / 结尾 / 。 / #### / 函数 / 体 / 和 / 文档 / 字符串 / 紧跟 / 在 / def / greet / _ / user / ( / ) / : / 后面 / 的 / 所有 / 缩 / 进行 / 构成 / 了 / 函数 / 体 / 。 / 第二行 / 的 / 文本 / 是 / 称为 / * / * / 文档 / 字符串 / * / * / （ / docstring / ） / 的 / 注释 / ， / 描述 / 了 / 函数 / 是 / 做 / 什么 / 的 / 。 / Python / 在 / 为 / 程序 / 中 / 的 / 函数 / 生成 / 文档 / 时 / ， / 会 / 查找 / 紧跟 / 在 / 函数 / 定义 / 后 / 的 / 字符串 / 。 / 这些 / 字符串 / 通常 / 前后 / 分别 / 用 / 三个 / 双引号 / 引起 / ， / 能够 / 包含 / 多行 / 。 / 代码 / 行 / print / ( / \" / Hello / ! / \" / ) / 是 / 函数 / 体内 / 的 / 唯一 / 行 / 代码 / ， / 因此 / greet / _ / user / ( / ) / 只 / 做 / 一项 / 工作 / ： / 打印 / Hello / ! / 。 / #### / 函数调用 / 要 / 使用 / 这个 / 函数 / ， / 必须 / 调用 / 它 / 。 / * / * / 函数调用 / * / * / 让 / Python / 执行 / 函数 / 中 / 的 / 代码 / 。 / 要 / 调用函数 / ， / 可 / 依次 / 指定 / 函数 / 名 / 以及 / 用 / 括号 / 括起 / 的 / 必要 / 信息 / 。 / 由于 / 这个 / 函数 / 不 / 需要 / 任何 / 信息 / ， / 调用 / 它 / 时 / 只 / 需 / 输入 / greet / _ / user / ( / ) / 即可 / 。 / 和 / 预期 / 的 / 一样 / ， / 它会 / 打印 / Hello / ! / ： / ` / ` / ` / Hello / ! / ` / ` / ` / ### / 向 / 函数 / 传递信息 / 只 / 需 / 稍作 / 修改 / ， / 就 / 可 / 让 / greet / _ / user / ( / ) / 函数 / 在 / 问候 / 用户 / 时以 / 其 / 名字 / 作为 / 抬头 / 。 / 为此 / ， / 可 / 在 / 函数 / 定义 / def / greet / _ / user / ( / ) / 的 / 括号 / 内 / 添加 / username / 。 / 这样 / ， / 可 / 让 / 函数 / 接受 / 你 / 给 / username / 指定 / 的 / 任何 / 值 / 。 / 现在 / ， / 这个 / 函数 / 要求 / 你 / 在 / 调用 / 它 / 时 / 给 / username / 指定 / 一个 / 值 / 。 / 因此 / 在 / 调用 / greet / _ / user / ( / ) / 时 / ， / 可 / 将 / 一个 / 名字 / 传递 / 给 / 它 / ， / 如下 / 所示 / ： / ` / ` / ` / python / def / greet / _ / user / ( / username / ) / : / \" / \" / \" / 显示 / 简单 / 的 / 问候语 / \" / \" / \" / print / ( / f / \" / Hello / , / { / username / . / title / ( / ) / } / ! / \" / ) / greet / _ / user / ( / ' / jesse / ' / ) / ` / ` / ` / #### / 函数调用 / 的 / 结果 / 代码 / greet / _ / user / ( / ' / jesse / ' / ) / 调用函数 / greet / _ / user / ( / ) / ， / 并 / 向 / 它 / 提供 / 执行 / 函数调用 / print / ( / ) / 所 / 需 / 的 / 信息 / 。 / 这个 / 函数 / 接受 / 你 / 传递 / 给 / 它 / 的 / 名字 / ， / 并 / 向 / 这个 / 人 / 发出 / 问候 / ： / ` / ` / ` / Hello / , / Jesse / ! / ` / ` / ` / 同样 / ， / greet / _ / user / ( / ' / sarah / ' / ) / 调用函数 / greet / _ / user / ( / ) / 并 / 向 / 它 / 传递 / ' / sarah / ' / ， / 从而 / 打印 / Hello / , / Sarah / ! / 。 / 你 / 可以 / 根据 / 需要 / 调用函数 / greet / _ / user / ( / ) / 任意 / 多次 / ， / 无论 / 在 / 调用 / 时 / 传入 / 什么 / 名字 / ， / 都 / 将 / 生成 / 相应 / 的 / 输出 / 。 / ### / 实参 / 和 / 形参 / 前面 / 在 / 定义 / greet / _ / user / ( / ) / 函数 / 时 / ， / 要求 / 给 / 变量 / username / 指定 / 一个 / 值 / 。 / 这样 / ， / 在 / 调用 / 这个 / 函数 / 并 / 提供 / 这种 / 信息 / （ / 人 / 名 / ） / 时 / ， / 它 / 将 / 打印 / 相应 / 的 / 问候语 / 。 / #### / 形参 / 的 / 概念 / 在 / greet / _ / user / ( / ) / 函数 / 的 / 定义 / 中 / ， / 变量 / username / 是 / 一个 / * / * / 形参 / * / * / （ / parameter / ） / ， / 即 / 函数 / 完成 / 工作 / 所 / 需 / 的 / 信息 / 。 / #### / 实参 / 的 / 概念 / 在 / 代码 / greet / _ / user / ( / ' / jesse / ' / ) / 中 / ， / 值 / ' / jesse / ' / 是 / 一个 / * / * / 实参 / * / * / （ / argument / ） / ， / 即 / 在 / 调用函数 / 时 / 传递 / 给 / 函数 / 的 / 信息 / 。 / 在 / 调用函数 / 时 / ， / 我们 / 将要 / 让 / 函数 / 使用 / 的 / 信息 / 放在 / 括号 / 内 / 。 / 在 / greet / _ / user / ( / ' / jesse / ' / ) / 这个 / 示例 / 中 / ， / 我们 / 将 / 实参 / ' / jesse / ' / 传递 / 给 / 函数 / greet / _ / user / ( / ) / ， / 这个 / 值 / 被 / 赋 / 给 / 了 / 形参 / username / 。 / 注意 / ： / 大家 / 有时候 / 会 / 形参 / 、 / 实参 / 不 / 分 / 。 / 即使 / 你 / 看到 / 有人 / 将 / 函数 / 定义 / 中 / 的 / 变量 / 称为 / 实参 / 或 / 将 / 函数调用 / 中 / 的 / 变量 / 称为 / 形参 / ， / 也 / 不要 / 大惊小怪 / 。 / ## / 传递 / 实参 / 函数 / 定义 / 中 / 可能 / 包含 / 多个 / 形参 / ， / 因此 / 函数调用 / 中 / 也 / 可能 / 包含 / 多个 / 实参 / 。 / 向 / 函数 / 传递 / 实参 / 的 / 方式 / 很多 / ： / 既 / 可以 / 使用 / * / * / 位置 / 实参 / * / * / ， / 这 / 要求 / 实参 / 的 / 顺序 / 与 / 形参 / 的 / 顺序 / 相同 / ； / 也 / 可以 / 使用 / * / * / 关键字 / 实参 / * / * / ， / 其中 / 每个 / 实参 / 都 / 由 / 变量名 / 和 / 值 / 组成 / ； / 还 / 可以 / 使用 / 列表 / 和 / 字典 / 。 / 下面 / 依次 / 介绍 / 这些 / 方式 / 。 / ### / 位置 / 实参 / 在 / 调用函数 / 时 / ， / Python / 必须 / 将 / 函数调用 / 中 / 的 / 每个 / 实参 / 关联 / 到 / 函数 / 定义 / 中 / 的 / 一个 / 形参 / 。 / 最 / 简单 / 的 / 方式 / 是 / 基于 / 实参 / 的 / 顺序 / 进行 / 关联 / 。 / 以 / 这种 / 方式 / 关联 / 的 / 实参 / 称为 / * / * / 位置 / 实参 / * / * / 。 / 为了 / 明白 / 其中 / 的 / 工作 / 原理 / ， / 我们 / 来看 / 一个 / 显示 / 宠物 / 信息 / 的 / 函数 / 。 / 这个 / 函数 / 指出 / 一个 / 宠物 / 属于 / 哪 / 种 / 动物 / 以及 / 它 / 叫 / 什么 / 名字 / ， / 如下 / 所示 / ： / #### / pets / . / py / 示例 / ` / ` / ` / python / def / describe / _ / pet / ( / animal / _ / type / , / pet / _ / name / ) / : / \" / \" / \" / 显示 / 宠物 / 的 / 信息 / \" / \" / \" / print / ( / f / \" / \\ / nI / have / a / { / animal / _ / type / } / . / \" / ) / print / ( / f / \" / My / { / animal / _ / type / } / ' / s / name / is / { / pet / _ / name / . / title / ( / ) / } / . / \" / ) / describe / _ / pet / ( / ' / hamster / ' / , / ' / harry / ' / ) / ` / ` / ` / 这个 / 函数 / 的 / 定义 / 表明 / ， / 它 / 需要 / 一个 / 动物 / 类型 / 和 / 一个 / 名字 / 。 / 在 / 调用 / describe / _ / pet / ( / ) / 时 / ， / 需要 / 按 / 顺序 / 提供 / 一个 / 动物 / 类型 / 和 / 一个 / 名字 / 。 / 例如 / ， / 在 / 刚才 / 的 / 函数调用 / 中 / ， / 实参 / ' / hamster / ' / 被 / 赋 / 给 / 形参 / animal / _ / type / ， / 而 / 实参 / ' / harry / ' / 被 / 赋 / 给 / 形参 / pet / _ / name / 。 / 在 / 函数 / 体内 / ， / 使用 / 这 / 两个 / 形参来 / 显示 / 宠物 / 的 / 信息 / 。 / 输出 / 描述 / 了 / 一只 / 名为 / Harry / 的 / 仓鼠 / ： / ` / ` / ` / I / have / a / hamster / . / My / hamster / ' / s / name / is / Harry / . / ` / ` / ` / #### / 调用函数 / 多次 / 可 / 根据 / 需要 / 调用函数 / 任意 / 多次 / 。 / 要 / 再 / 描述 / 一个 / 宠物 / ， / 只 / 需 / 再次 / 调用 / describe / _ / pet / ( / ) / 即可 / ： / ` / ` / ` / python / def / describe / _ / pet / ( / animal / _ / type / , / pet / _ / name / ) / : / \" / \" / \" / 显示 / 宠物 / 的 / 信息 / \" / \" / \" / print / ( / f / \" / \\ / nI / have / a / { / animal / _ / type / } / . / \" / ) / print / ( / f / \" / My / { / animal / _ / type / } / ' / s / name / is / { / pet / _ / name / . / title / ( / ) / } / . / \" / ) / describe / _ / pet / ( / ' / hamster / ' / , / ' / harry / ' / ) / describe / _ / pet / ( / ' / dog / ' / , / ' / willie / ' / ) / ` / ` / ` / 第二次 / 调用 / describe / _ / pet / ( / ) / 函数 / 时 / ， / 向 / 它 / 传递 / 实参 / ' / dog / ' / 和 / ' / willie / ' / 。 / 与 / 第一次 / 调用 / 时 / 一样 / ， / Python / 将 / 实参 / ' / dog / ' / 关联 / 到 / 形参 / animal / _ / type / ， / 并 / 将 / 实参 / ' / willie / ' / 关联 / 到 / 形参 / pet / _ / name / 。 / 与 / 前面 / 一样 / ， / 这个 / 函数 / 完成 / 了 / 任务 / ， / 但 / 打印 / 的 / 是 / 一条 / 名为 / Willie / 的 / 小狗 / 的 / 信息 / 。 / 至此 / ， / 有 / 一只 / 名为 / Harry / 的 / 仓鼠 / ， / 还有 / 一条 / 名为 / Willie / 的 / 小狗 / ： / ` / ` / ` / I / have / a / hamster / . / My / hamster / ' / s / name / is / Harry / . / I / have / a / dog / . / My / dog / ' / s / name / is / Willie / . / ` / ` / ` / 多次 / 调用 / 同一个 / 函数 / 是 / 一种 / 效率 / 极高 / 的 / 工作 / 方式 / 。 / 只 / 需 / 在 / 函数 / 中 / 编写 / 一次 / 描述 / 宠物 / 的 / 代码 / ， / 每当 / 需要 / 描述 / 新 / 宠物 / 时 / ， / 就 / 都 / 可以 / 调用 / 这个 / 函数 / 并 / 向 / 它 / 提供 / 新 / 宠物 / 的 / 信息 / 。 / 即便 / 描述 / 宠物 / 的 / 代码 / 增加 / 到 / 了 / 10 / 行 / ， / 依然 / 只 / 需 / 使用 / 一行 / 调用函数 / 的 / 代码 / ， / 就 / 可以 / 描述 / 一个 / 新 / 宠物 / 。 / 在 / 函数 / 中 / ， / 可 / 根据 / 需要 / 使用 / 任意 / 数量 / 的 / 位置 / 实参 / ， / Python / 将 / 按 / 顺序 / 将 / 函数调用 / 中 / 的 / 实参 / 关联 / 到 / 函数 / 定义 / 中 / 相应 / 的 / 形参 / 。 / #### / 位置 / 实参 / 的 / 顺序 / 很 / 重要 / 当 / 使用 / 位置 / 实参 / 来 / 调用函数 / 时 / ， / 如果 / 实参 / 的 / 顺序 / 不 / 正确 / ， / 结果 / 可能 / 会 / 出乎意料 / ： / ` / ` / ` / python / def / describe / _ / pet / ( / animal / _ / type / , / pet / _ / name / ) / : / \" / \" / \" / 显示 / 宠物 / 的 / 信息 / \" / \" / \" / print / ( / f / \" / \\ / nI / have / a / { / animal / _ / type / } / . / \" / ) / print / ( / f / \" / My / { / animal / _ / type / } / ' / s / name / is / { / pet / _ / name / . / title / ( / ) / } / . / \" / ) / describe / _ / pet / ( / ' / harry / ' / , / ' / hamster / ' / ) / ` / ` / ` / 在 / 这个 / 函数调用 / 中 / ， / 先 / 指定 / 名字 / ， / 再 / 指定 / 动物 / 类型 / 。 / 由于 / 实参 / ' / harry / ' / 在 / 前 / ， / 这个 / 值 / 将 / 被 / 赋 / 给 / 形参 / animal / _ / type / ， / 而 / 后面 / 的 / ' / hamster / ' / 将 / 被 / 赋 / 给 / 形参 / pet / _ / name / 。 / 结果 / 是 / 有 / 一个 / 名为 / Hamster / 的 / harry / ： / ` / ` / ` / I / have / a / harry / . / My / harry / ' / s / name / is / Hamster / . / ` / ` / ` / 如果 / 你 / 得到 / 的 / 结果 / 像 / 上面 / 一样 / 可笑 / ， / 请 / 确认 / 函数调用 / 中 / 实参 / 的 / 顺序 / 与 / 函数 / 定义 / 中形 / 参 / 的 / 顺序 / 是否 / 一致 / 。 / ### / 关键字 / 实参 / * / * / 关键字 / 实参 / * / * / 是 / 传递 / 给 / 函数 / 的 / 名值 / 对 / 。 / 这样 / 会 / 直接 / 在 / 实参 / 中将 / 名称 / 和 / 值 / 关联 / 起来 / ， / 因此 / 向 / 函数 / 传递 / 实参 / 时 / 就 / 不会 / 混淆 / 了 / （ / 不会 / 得到 / 名为 / Hamster / 的 / harry / 这样 / 的 / 结果 / ） / 。 / 关键字 / 实参 / 不仅 / 让 / 你 / 无须 / 考虑 / 函数调用 / 中 / 的 / 实参 / 顺序 / ， / 而且 / 清楚 / 地 / 指出 / 了 / 函数调用 / 中 / 各个 / 值 / 的 / 用途 / 。 / 下面 / 重新 / 编写 / pets / . / py / ， / 在 / 其中 / 使用 / 关键字 / 实参 / 来 / 调用 / describe / _ / pet / ( / ) / ： / ` / ` / ` / python / def / describe / _ / pet / ( / animal / _ / type / , / pet / _ / name / ) / : / \" / \" / \" / 显示 / 宠物 / 的 / 信息 / \" / \" / \" / print / ( / f / \" / \\ / nI / have / a / { / animal / _ / type / } / . / \" / ) / print / ( / f / \" / My / { / animal / _ / type / } / ' / s / name / is / { / pet / _ / name / . / title / ( / ) / } / . / \" / ) / describe / _ / pet / ( / animal / _ / type / = / ' / hamster / ' / , / pet / _ / name / = / ' / harry / ' / ) / ` / ` / ` / #### / 关键字 / 实参 / 的 / 优势 / describe / _ / pet / ( / ) / 函数 / 还 / 和 / 之前 / 一样 / ， / 但 / 这次 / 调用 / 这个 / 函数 / 时 / ， / 向 / Python / 明确 / 地 / 指出 / 了 / 各个 / 实参 / 对应 / 的 / 形参 / 。 / 当 / 看到 / 这个 / 函数调用 / 时 / ， / Python / 知道 / 应该 / 将 / 实参 / ' / hamster / ' / 和 / ' / harry / ' / 分别 / 赋给 / 形参 / animal / _ / type / 和 / pet / _ / name / 。 / 输出 / 正确 / 无误 / ， / 指出 / 有 / 一只 / 名为 / Harry / 的 / 仓鼠 / 。 / 关键字 / 实参 / 的 / 顺序 / 无关紧要 / ， / 因为 / Python / 知道 / 各个 / 值该 / 被 / 赋 / 给 / 哪个 / 形参 / 。 / 下面 / 两个 / 函数调用 / 是 / 等效 / 的 / ： / ` / ` / ` / python / describe / _ / pet / ( / animal / _ / type / = / ' / hamster / ' / , / pet / _ / name / = / ' / harry / ' / ) / describe / _ / pet / ( / pet / _ / name / = / ' / harry / ' / , / animal / _ / type / = / ' / hamster / ' / ) / ` / ` / ` / 注意 / ： / 在 / 使用 / 关键字 / 实参 / 时 / ， / 务必 / 准确 / 地 / 指定 / 函数 / 定义 / 中 / 的 / 形 / 参名 / 。 / ### / 默认值 / 在 / 编写 / 函数 / 时 / ， / 可以 / 给 / 每个 / 形参 / 指定 / * / * / 默认值 / * / * / 。 / 如果 / 在 / 调用函数 / 中 / 给 / 形参 / 提供 / 了 / 实参 / ， / Python / 将 / 使用 / 指定 / 的 / 实参 / 值 / ； / 否则 / ， / 将 / 使用 / 形参 / 的 / 默认值 / 。 / 因此 / ， / 给 / 形参 / 指定 / 默认值 / 后 / ， / 可 / 在 / 函数调用 / 中 / 省略 / 相应 / 的 / 实参 / 。 / 使用 / 默认值 / 不仅 / 能 / 简化 / 函数调用 / ， / 还 / 能 / 清楚 / 地 / 指出 / 函数 / 的 / 典型 / 用法 / 。 / 如果 / 你 / 发现 / 在 / 调用 / describe / _ / pet / ( / ) / 时 / ， / 描述 / 的 / 大多 / 是 / 小狗 / ， / 就 / 可 / 将 / 形参 / animal / _ / type / 的 / 默认值 / 设置 / 为 / ' / dog / ' / 。 / 这样 / ， / 当 / 调用 / describe / _ / pet / ( / ) / 来 / 描述 / 小狗 / 时 / ， / 就 / 可以 / 不 / 提供 / 该 / 信息 / ： / ` / ` / ` / python / def / describe / _ / pet / ( / pet / _ / name / , / animal / _ / type / = / ' / dog / ' / ) / : / \" / \" / \" / 显示 / 宠物 / 的 / 信息 / \" / \" / \" / print / ( / f / \" / \\ / nI / have / a / { / animal / _ / type / } / . / \" / ) / print / ( / f / \" / My / { / animal / _ / type / } / ' / s / name / is / { / pet / _ / name / . / title / ( / ) / } / . / \" / ) / describe / _ / pet / ( / pet / _ / name / = / ' / willie / ' / ) / ` / ` / ` / #### / 默认值 / 的 / 使用 / 规则 / 这里 / 修改 / 了 / describe / _ / pet / ( / ) / 函数 / 的 / 定义 / ， / 在 / 其中 / 给 / 形参 / animal / _ / type / 指定 / 了 / 默认值 / ' / dog / ' / 。 / 这样 / ， / 在 / 调用 / 这个 / 函数 / 时 / ， / 如果 / 没有 / 给 / animal / _ / type / 指定 / 值 / ， / Python / 将 / 自动 / 把 / 这个 / 形参 / 设置 / 为 / ' / dog / ' / ： / ` / ` / ` / I / have / a / dog / . / My / dog / ' / s / name / is / Willie / . / ` / ` / ` / 请 / 注意 / ， / 在 / 这个 / 函数 / 的 / 定义 / 中 / ， / 修改 / 了 / 形参 / 的 / 排列 / 顺序 / 。 / 由于 / 给 / animal / _ / type / 指定 / 了 / 默认值 / ， / 无须 / 通过 / 实参 / 来 / 指定 / 动物 / 类型 / ， / 因此 / 函数调用 / 只 / 包含 / 一个 / 实参 / — / — / 宠物 / 的 / 名字 / 。 / 然而 / ， / Python / 依然 / 将 / 这个 / 实参 / 视为 / 位置 / 实参 / ， / 如果 / 函数调用 / 只 / 包含 / 宠物 / 的 / 名字 / ， / 这个 / 实参 / 将 / 被 / 关联 / 到 / 函数 / 定义 / 中 / 的 / 第一个 / 形参 / 。 / 这 / 就是 / 需要 / 将 / pet / _ / name / 放在 / 形参 / 列表 / 开头 / 的 / 原因 / 。 / 现在 / ， / 使用 / 这个 / 函数 / 的 / 最 / 简单 / 方式 / 是 / ， / 在 / 函数调用 / 中 / 只 / 提供 / 小狗 / 的 / 名字 / ： / ` / ` / ` / python / describe / _ / pet / ( / ' / willie / ' / ) / ` / ` / ` / 这个 / 函数调用 / 的 / 输出 / 与 / 前 / 一个 / 示例 / 相同 / 。 / 只 / 提供 / 了 / 一个 / 实参 / ' / willie / ' / ， / 这个 / 实参 / 将 / 被 / 关联 / 到 / 函数 / 定义 / 中 / 的 / 第一个 / 形参 / pet / _ / name / 。 / 由于 / 没有 / 给 / animal / _ / type / 提供 / 实参 / ， / 因此 / Python / 使用 / 默认值 / ' / dog / ' / 。 / 如果 / 要 / 描述 / 的 / 动物 / 不是 / 小狗 / ， / 可 / 使用 / 类似 / 于 / 下面 / 的 / 函数调用 / ： / ` / ` / ` / python / describe / _ / pet / ( / pet / _ / name / = / ' / harry / ' / , / animal / _ / type / = / ' / hamster / ' / ) / ` / ` / ` / 由于 / 显式 / 地 / 给 / animal / _ / type / 提供 / 了 / 实参 / ， / Python / 将 / 忽略 / 这个 / 形参 / 的 / 默认值 / 。 / 注意 / ： / 当 / 使用 / 默认值 / 时 / ， / 必须 / 在 / 形参 / 列表 / 中先 / 列出 / 没有 / 默认值 / 的 / 形参 / ， / 再 / 列出 / 有 / 默认值 / 的 / 形参 / 。 / 这 / 让 / Python / 依然 / 能够 / 正确 / 地 / 解读 / 位置 / 实参 / 。 / ### / 等效 / 的 / 函数调用 / 鉴于 / 可 / 混合 / 使用 / 位置 / 实参 / 、 / 关键字 / 实参 / 和 / 默认值 / ， / 通常 / 有 / 多种 / 等效 / 的 / 函数调用 / 方式 / 。 / 请 / 看 / describe / _ / pet / ( / ) / 函数 / 的 / 如下 / 定义 / ， / 其中 / 给 / 一个 / 形参 / 提供 / 了 / 默认值 / ： / ` / ` / ` / python / def / describe / _ / pet / ( / pet / _ / name / , / animal / _ / type / = / ' / dog / ' / ) / : / ` / ` / ` / 基于 / 这种 / 定义 / ， / 在 / 任何 / 情况 / 下 / 都 / 必须 / 给 / pet / _ / name / 提供 / 实参 / 。 / 在 / 指定 / 该 / 实参 / 时 / ， / 既 / 可以 / 使用 / 位置 / 实参 / ， / 也 / 可以 / 使用 / 关键字 / 实参 / 。 / 如果 / 要 / 描述 / 的 / 动物 / 不是 / 小狗 / ， / 还 / 必须 / 在 / 函数调用 / 中 / 给 / animal / _ / type / 提供 / 实参 / 。 / 同样 / ， / 在 / 指定 / 该 / 实参 / 时 / ， / 既 / 可以 / 使用 / 位置 / 实参 / ， / 也 / 可以 / 使用 / 关键字 / 实参 / 。 / 下面 / 对 / 这个 / 函数 / 的 / 所有 / 调用 / 都 / 可行 / ： / ` / ` / ` / python / # / 一条 / 名为 / Willie / 的 / 小狗 / describe / _ / pet / ( / ' / willie / ' / ) / describe / _ / pet / ( / pet / _ / name / = / ' / willie / ' / ) / # / 一只 / 名为 / Harry / 的 / 仓鼠 / describe / _ / pet / ( / ' / harry / ' / , / ' / hamster / ' / ) / describe / _ / pet / ( / pet / _ / name / = / ' / harry / ' / , / animal / _ / type / = / ' / hamster / ' / ) / describe / _ / pet / ( / animal / _ / type / = / ' / hamster / ' / , / pet / _ / name / = / ' / harry / ' / ) / ` / ` / ` / 这些 / 函数调用 / 的 / 输出 / 与 / 前面 / 的 / 示例 / 相同 / 。 / 使用 / 哪 / 种 / 调用 / 方式 / 无关紧要 / 。 / 可以 / 使用 / 对 / 你 / 来说 / 最 / 容易 / 理解 / 的 / 调用 / 方式 / ， / 只要 / 函数调用 / 能 / 生成 / 你 / 期望 / 的 / 输出 / 就 / 好 / 。 / ### / 避免 / 实参 / 错误 / 等 / 你 / 开始 / 使用 / 函数 / 后 / ， / 也许 / 会 / 遇到 / 实参 / 不 / 匹配 / 错误 / 。 / 当 / 你 / 提供 / 的 / 实参 / 多于 / 或 / 少于 / 函数 / 完成 / 工作 / 所 / 需 / 的 / 实参 / 数量 / 时 / ， / 将 / 出现 / 实参 / 不 / 匹配 / 错误 / 。 / 如果 / 在 / 调用 / describe / _ / pet / ( / ) / 函数 / 时 / 没有 / 指定 / 任何 / 实参 / ， / 结果 / 将 / 如何 / 呢 / ？ / ` / ` / ` / python / def / describe / _ / pet / ( / animal / _ / type / , / pet / _ / name / ) / : / \" / \" / \" / 显示 / 宠物 / 的 / 信息 / \" / \" / \" / print / ( / f / \" / \\ / nI / have / a / { / animal / _ / type / } / . / \" / ) / print / ( / f / \" / My / { / animal / _ / type / } / ' / s / name / is / { / pet / _ / name / . / title / ( / ) / } / . / \" / ) / describe / _ / pet / ( / ) / ` / ` / ` / #### / 错误信息 / 的 / 解读 / Python / 发现 / 该 / 函数调用 / 缺少 / 必要 / 的 / 信息 / ， / 并用 / traceback / 指出 / 了 / 这 / 一点 / ： / ` / ` / ` / Traceback / ( / most / recent / call / last / ) / : / File / \" / pets / . / py / \" / , / line / 6 / , / in / < / module / > / describe / _ / pet / ( / ) / ^ / ^ / ^ / ^ / ^ / ^ / ^ / ^ / ^ / ^ / ^ / ^ / ^ / ^ / TypeError / : / describe / _ / pet / ( / ) / missing / 2 / required / positional / arguments / : / ' / animal / _ / type / ' / and / ' / pet / _ / name / ' / ` / ` / ` / traceback / 首先 / 指出 / 问题 / 出 / 在 / 什么 / 地方 / ， / 让 / 我们 / 能够 / 回过 / 头去 / 找出 / 函数调用 / 中 / 的 / 错误 / 。 / 然后 / ， / 指出 / 导致 / 问题 / 的 / 函数调用 / 。 / 最后 / ， / traceback / 指出 / 该 / 函数调用 / 缺少 / 两个 / 实参 / ， / 并 / 指出 / 了 / 相应 / 形参 / 的 / 名称 / 。 / 如果 / 这个 / 函数 / 存储 / 在 / 一个 / 独立 / 的 / 文件 / 中 / ， / 我们 / 也许 / 无须 / 打开 / 这个 / 文件 / 并 / 查看 / 函数 / 的 / 代码 / ， / 就 / 能 / 重新 / 正确 / 地 / 编写 / 函数调用 / 。 / Python / 能 / 读取 / 函数 / 的 / 代码 / ， / 并 / 指出 / 需要 / 为 / 哪些 / 形参 / 提供 / 实参 / ， / 这为 / 我们 / 提供 / 了 / 极大 / 的 / 帮助 / 。 / 这是 / 应该 / 给 / 变量 / 和 / 函数 / 指定 / 描述性 / 名称 / 的 / 另 / 一个 / 原因 / ： / 如果 / 这样 / 做 / 了 / ， / 那么 / 无论 / 对于 / 你 / ， / 还是 / 可能 / 使用 / 你 / 编写 / 的 / 代码 / 的 / 其他 / 任何人 / 来说 / ， / Python / 提供 / 的 / 错误 / 消息 / 都 / 将 / 更 / 有 / 帮助 / 性 / 。 / 如果 / 提供 / 的 / 实参 / 太 / 多 / ， / 将 / 出现 / 类似 / 的 / traceback / ， / 帮助 / 你 / 确保 / 函数调用 / 和 / 函数 / 定义 / 匹配 / 。 / ## / 返回值 / 函数 / 并非 / 总是 / 直接 / 显示 / 输出 / ， / 它 / 还 / 可以 / 处理 / 一些 / 数据 / ， / 并 / 返回 / 一个 / 或 / 一组 / 值 / 。 / 函数 / 返回 / 的 / 值 / 称为 / * / * / 返回值 / * / * / 。 / 在 / 函数 / 中 / ， / 可以 / 使用 / return / 语句 / 将值 / 返回 / 到 / 调用函数 / 的 / 那行 / 代码 / 。 / 返回值 / 让 / 你 / 能够 / 将 / 程序 / 的 / 大部分 / 繁重 / 工作 / 移 / 到 / 函数 / 中 / 完成 / ， / 从而 / 简化 / 主程序 / 。 / ### / 返回 / 简单 / 的 / 值 / 下面 / 来看 / 一个 / 函数 / ， / 它 / 接受 / 名和姓 / 并 / 返回 / 标准 / 格式 / 的 / 姓名 / ： / #### / formatted / _ / name / . / py / 示例 / ` / ` / ` / python / def / get / _ / formatted / _ / name / ( / first / _ / name / , / last / _ / name / ) / : / \" / \" / \" / 返回 / 标准 / 格式 / 的 / 姓名 / \" / \" / \" / full / _ / name / = / f / \" / { / first / _ / name / } / { / last / _ / name / } / \" / return / full / _ / name / . / title / ( / ) / musician / = / get / _ / formatted / _ / name / ( / ' / jimi / ' / , / ' / hendrix / ' / ) / print / ( / musician / ) / ` / ` / ` / get / _ / formatted / _ / name / ( / ) / 函数 / 的 / 定义 / 通过 / 形参 / 接受 / 名和姓 / 。 / 它 / 将 / 名 / 和 / 姓 / 合在一起 / ， / 在 / 中间 / 加上 / 一个 / 空格 / ， / 并 / 将 / 结果 / 赋给 / 变量 / full / _ / name / 。 / 然后 / ， / 它 / 将 / full / _ / name / 的 / 值 / 转换 / 为首 / 字母 / 大写 / 的 / 格式 / ， / 并 / 将 / 结果 / 返回 / 函数调用 / 行 / 。 / 在 / 调用 / 可以 / 返回值 / 的 / 函数 / 时 / ， / 需要 / 提供 / 一个 / 变量 / ， / 以便 / 将 / 返回 / 的 / 值 / 赋 / 给 / 它 / 。 / 这里 / 将 / 返回值 / 赋给 / 了 / 变量 / musician / 。 / 输出 / 为 / 标准 / 格式 / 的 / 姓名 / ： / ` / ` / ` / Jimi / Hendrix / ` / ` / ` / 原本 / 只 / 需 / 编写 / 下面 / 的 / 代码 / 就 / 可以 / 输出 / 这个 / 标准 / 格式 / 的 / 姓名 / ， / 前面 / 做 / 的 / 工作 / 好像 / 太多 / 了 / ： / ` / ` / ` / python / print / ( / \" / Jimi / Hendrix / \" / ) / ` / ` / ` / 你 / 要 / 知道 / ， / 在 / 需要 / 分别 / 存储 / 大量 / 名和姓 / 的 / 大型 / 程序 / 中 / ， / 像 / get / _ / formatted / _ / name / ( / ) / 这样 / 的 / 函数 / 非常 / 有用 / 。 / 你 / 可以 / 分别 / 存储 / 名和姓 / ， / 每当 / 需要 / 显示 / 姓名 / 时 / 就 / 调用 / 这个 / 函数 / 。 / ### / 让 / 实参 / 变成 / 可选 / 的 / 有时候 / ， / 需要 / 让 / 实参 / 变成 / 可选 / 的 / ， / 以便 / 使用 / 函数 / 的 / 人 / 只 / 在 / 必要 / 时才 / 提供 / 额外 / 的 / 信息 / 。 / 可以 / 使用 / 默认值 / 来 / 让 / 实参 / 变成 / 可选 / 的 / 。 / 假设 / 要 / 扩展 / get / _ / formatted / _ / name / ( / ) / 函数 / ， / 使 / 其 / 除了 / 名和姓 / 之外 / 还 / 可以 / 处理 / 中间 / 名 / 。 / 为此 / ， / 可 / 将 / 其 / 修改 / 成 / 类似 / 这样 / ： / ` / ` / ` / python / def / get / _ / formatted / _ / name / ( / first / _ / name / , / middle / _ / name / , / last / _ / name / ) / : / \" / \" / \" / 返回 / 标准 / 格式 / 的 / 姓名 / \" / \" / \" / full / _ / name / = / f / \" / { / first / _ / name / } / { / middle / _ / name / } / { / last / _ / name / } / \" / return / full / _ / name / . / title / ( / ) / musician / = / get / _ / formatted / _ / name / ( / ' / john / ' / , / ' / lee / ' / , / ' / hooker / ' / ) / print / ( / musician / ) / ` / ` / ` / 只要 / 同时 / 提供 / 名 / 、 / 中间 / 名和姓 / ， / 这个 / 函数 / 就 / 能 / 正确 / 运行 / 。 / 它 / 根据 / 这三 / 部分 / 创建 / 一个 / 字符串 / ， / 在 / 适当 / 的 / 地方 / 加上 / 空格 / ， / 并 / 将 / 结果 / 转换 / 为首 / 字母 / 大写 / 的 / 格式 / ： / ` / ` / ` / John / Lee / Hooker / ` / ` / ` / #### / 处理 / 可选 / 的 / 中间 / 名 / 然而 / ， / 并非 / 所有人 / 都 / 有 / 中间 / 名 / 。 / 如果 / 调用 / 这个 / 函数 / 时 / 只 / 提供 / 了 / 名 / 和 / 姓 / ， / 它 / 将 / 不能 / 正确 / 地 / 运行 / 。 / 为 / 让 / 中间 / 名 / 变成 / 可选 / 的 / ， / 可给 / 形参 / middle / _ / name / 指定 / 默认值 / （ / 空 / 字符串 / ） / ， / 在 / 用户 / 不 / 提供 / 中间 / 名时 / 不 / 使用 / 这个 / 形参 / 。 / 为了 / 让 / get / _ / formatted / _ / name / ( / ) / 在 / 没有 / 提供 / 中间 / 名时 / 依然 / 正确 / 运行 / ， / 可给 / 形参 / middle / _ / name / 指定 / 默认值 / （ / 空 / 字符串 / ） / ， / 并 / 将 / 其移 / 到 / 形参 / 列表 / 的 / 末尾 / ： / ` / ` / ` / python / def / get / _ / formatted / _ / name / ( / first / _ / name / , / last / _ / name / , / middle / _ / name / = / ' / ' / ) / : / \" / \" / \" / 返回 / 标准 / 格式 / 的 / 姓名 / \" / \" / \" / if / middle / _ / name / : / full / _ / name / = / f / \" / { / first / _ / name / } / { / middle / _ / name / } / { / last / _ / name / } / \" / else / : / full / _ / name / = / f / \" / { / first / _ / name / } / { / last / _ / name / } / \" / return / full / _ / name / . / title / ( / ) / musician / = / get / _ / formatted / _ / name / ( / ' / jimi / ' / , / ' / hendrix / ' / ) / print / ( / musician / ) / musician / = / get / _ / formatted / _ / name / ( / ' / john / ' / , / ' / hooker / ' / , / ' / lee / ' / ) / print / ( / musician / ) / ` / ` / ` / 在 / 这个 / 示例 / 中 / ， / 姓名 / 是 / 根据 / 三个 / 可能 / 提供 / 的 / 部分 / 创建 / 的 / 。 / 每个 / 人 / 都 / 有名 / 和 / 姓 / ， / 因此 / 在 / 函数 / 定义 / 中 / 首先 / 列出 / 了 / 这 / 两个 / 形参 / 。 / 中间 / 名是 / 可选 / 的 / ， / 因此 / 在 / 函数 / 定义 / 中 / 最后 / 列出 / 该 / 形参 / ， / 并 / 将 / 其 / 默认值 / 设置 / 为空 / 字符串 / 。 / 在 / 函数 / 体中 / ， / 检查 / 是否 / 提供 / 了 / 中间 / 名 / 。 / Python / 将 / 非空 / 字符串 / 解读 / 为 / True / ， / 如果 / 在 / 函数调用 / 中 / 提供 / 了 / 中间 / 名 / ， / 条件 / 测试 / if / middle / _ / name / 将 / 为 / True / 。 / 如果 / 提供 / 了 / 中间 / 名 / ， / 就 / 将 / 名 / 、 / 中间 / 名和姓 / 合并 / 为 / 姓名 / ， / 再 / 将 / 其 / 修改 / 为首 / 字母 / 大写 / 的 / 格式 / ， / 并 / 将 / 结果 / 返回 / 函数调用 / 行 / 。 / 在 / 函数调用 / 行 / ， / 将 / 返回 / 的 / 值 / 赋 / 给 / 变量 / musician / 。 / 最后 / ， / 这个 / 变量 / 的 / 值 / 被 / 打印 / 了 / 出来 / 。 / 如果 / 没有 / 提供 / 中间 / 名 / ， / middle / _ / name / 将 / 为 / 空 / 字符串 / ， / 导致 / if / 测试 / 未 / 通过 / ， / 进而 / 执行 / else / 代码 / 块 / ： / 只 / 使用 / 名和姓 / 来 / 生成 / 姓名 / ， / 并 / 将 / 设置 / 好 / 格式 / 的 / 姓名 / 返回 / 函数调用 / 行 / 。 / 在 / 函数调用 / 行 / ， / 将 / 返回 / 的 / 值 / 赋 / 给 / 变量 / musician / 。 / 最后 / ， / 这个 / 变量 / 的 / 值 / 被 / 打印 / 了 / 出来 / 。 / 在 / 调用 / 这个 / 函数 / 时 / ， / 如果 / 只想 / 指定 / 名和姓 / ， / 调用 / 起来 / 将 / 非常简单 / 。 / 如果 / 还要 / 指定 / 中间 / 名 / ， / 就 / 必须 / 确保 / 它 / 是 / 最后 / 一个 / 实参 / ， / 这样 / Python / 才能 / 正确 / 地 / 将 / 位置 / 实参 / 关联 / 到 / 形参 / 。 / 这个 / 修改 / 后 / 的 / 版本 / 不仅 / 适用 / 于 / 只有 / 名和姓 / 的 / 人 / ， / 也 / 适用 / 于 / 还有 / 中间 / 名 / 的 / 人 / ： / ` / ` / ` / Jimi / Hendrix / John / Lee / Hooker / ` / ` / ` / 可选值 / 在 / 让 / 函数 / 能够 / 处理 / 各种 / 不同 / 情形 / 的 / 同时 / ， / 确保 / 函数调用 / 尽可能 / 简单 / 。 / ### / 返回 / 字典 / 函数 / 可 / 返回 / 任何 / 类型 / 的 / 值 / ， / 包括 / 列表 / 和 / 字典 / 等 / 较为 / 复杂 / 的 / 数据结构 / 。 / 例如 / ， / 下面 / 的 / 函数 / 接受 / 姓名 / 的 / 组成部分 / ， / 并 / 返回 / 一个 / 表示 / 人 / 的 / 字典 / ： / #### / person / . / py / 示例 / ` / ` / ` / python / def / build / _ / person / ( / first / _ / name / , / last / _ / name / ) / : / \" / \" / \" / 返回 / 一个 / 字典 / ， / 其中 / 包含 / 有关 / 一个 / 人 / 的 / 信息 / \" / \" / \" / person / = / { / ' / first / ' / : / first / _ / name / , / ' / last / ' / : / last / _ / name / } / return / person / musician / = / build / _ / person / ( / ' / jimi / ' / , / ' / hendrix / ' / ) / print / ( / musician / ) / ` / ` / ` / build / _ / person / ( / ) / 函数 / 接受 / 名和姓 / ， / 并 / 将 / 这些 / 值 / 放在 / 字典 / 中 / 。 / 在 / 存储 / first / _ / name / 的 / 值时 / ， / 使用 / 的 / 键 / 为 / ' / first / ' / ， / 而 / 在 / 存储 / last / _ / name / 的 / 值时 / ， / 使用 / 的 / 键 / 为 / ' / last / ' / 。 / 然后 / ， / 返回 / 表示 / 人 / 的 / 整个 / 字典 / 。 / 在 / 此处 / ， / 打印 / 这个 / 被 / 返回 / 的 / 值 / 。 / 此时 / ， / 原来 / 的 / 两项 / 文本 / 信息 / 存储 / 在 / 一个 / 字典 / 中 / ： / ` / ` / ` / { / ' / first / ' / : / ' / jimi / ' / , / ' / last / ' / : / ' / hendrix / ' / } / ` / ` / ` / #### / 扩展 / 字典 / 功能 / 这个 / 函数 / 接受 / 简单 / 的 / 文本 / 信息 / ， / 并 / 将 / 其 / 放在 / 一个 / 更 / 合适 / 的 / 数据结构 / 中 / ， / 让 / 你 / 不仅 / 能 / 打印 / 这些 / 信息 / ， / 还 / 能 / 以 / 其他 / 方式 / 处理 / 它们 / 。 / 当前 / ， / 字符串 / ' / jimi / ' / 和 / ' / hendrix / ' / 分别 / 被 / 标记 / 为名 / 和 / 姓 / 。 / 你 / 可以 / 轻松 / 地 / 扩展 / 这个 / 函数 / ， / 使 / 其 / 接受 / 可选值 / ， / 如 / 中间 / 名 / 、 / 年龄 / 、 / 职业 / 或 / 其他 / 任何 / 要 / 存储 / 的 / 信息 / 。 / 例如 / ， / 下面 / 的 / 修改 / 能 / 让 / 你 / 存储 / 年龄 / ： / ` / ` / ` / python / def / build / _ / person / ( / first / _ / name / , / last / _ / name / , / age / = / None / ) / : / \" / \" / \" / 返回 / 一个 / 字典 / ， / 其中 / 包含 / 有关 / 一个 / 人 / 的 / 信息 / \" / \" / \" / person / = / { / ' / first / ' / : / first / _ / name / , / ' / last / ' / : / last / _ / name / } / if / age / : / person / [ / ' / age / ' / ] / = / age / return / person / ` / ` / ` / 在 / 函数 / 定义 / 中 / ， / 新增 / 了 / 一个 / 可选 / 形参 / age / ， / 其 / 默认值 / 被 / 设置 / 为 / 特殊 / 值 / None / （ / 表示 / 变量 / 没有 / 值 / ） / 。 / 可 / 将 / None / 视为 / 占位 / 值 / 。 / 在 / 条件 / 测试 / 中 / ， / None / 相当于 / False / 。 / 如果 / 函数调用 / 中 / 包含 / 形参 / age / 的 / 值 / ， / 这个 / 值 / 将 / 被 / 存储 / 到 / 字典 / 中 / 。 / 在 / 任何 / 情况 / 下 / ， / 这个 / 函数 / 都 / 会 / 存储 / 一个 / 人 / 的 / 姓名 / ， / 并且 / 可以 / 修改 / 它 / ， / 使 / 其 / 同时 / 存储 / 有关 / 这个 / 人 / 的 / 其他 / 信息 / 。 / ### / 结合 / 使用 / 函数 / 和 / while / 循环 / 可 / 将 / 函数 / 与 / 本书 / 前面 / 介绍 / 的 / 所有 / Python / 结构 / 结合 / 起来 / 使用 / 。 / 例如 / ， / 下面 / 将 / 结合 / 使用 / get / _ / formatted / _ / name / ( / ) / 函数 / 和 / while / 循环 / ， / 以 / 更 / 正规 / 的 / 方式 / 问候 / 用户 / 。 / 下面 / 尝试 / 使用 / 名和姓 / 跟 / 用户 / 打招呼 / ： / #### / greeter / . / py / 示例 / ` / ` / ` / python / def / get / _ / formatted / _ / name / ( / first / _ / name / , / last / _ / name / ) / : / \" / \" / \" / 返回 / 规范 / 格式 / 的 / 姓名 / \" / \" / \" / full / _ / name / = / f / \" / { / first / _ / name / } / { / last / _ / name / } / \" / return / full / _ / name / . / title / ( / ) / # / 这是 / 一个 / 无限 / 循环 / ! / while / True / : / print / ( / \" / \\ / nPlease / tell / me / your / name / : / \" / ) / f / _ / name / = / input / ( / \" / First / name / : / \" / ) / l / _ / name / = / input / ( / \" / Last / name / : / \" / ) / formatted / _ / name / = / get / _ / formatted / _ / name / ( / f / _ / name / , / l / _ / name / ) / print / ( / f / \" / \\ / nHello / , / { / formatted / _ / name / } / ! / \" / ) / ` / ` / ` / 在 / 这个 / 示例 / 中 / ， / 使用 / 的 / 是 / get / _ / formatted / _ / name / ( / ) / 的 / 简单 / 版本 / ， / 不 / 涉及 / 中间 / 名 / 。 / while / 循环 / 让 / 用户 / 输入 / 姓名 / ： / 提示 / 用户 / 依次 / 输入 / 名和姓 / 。 / #### / 提供 / 退出 / 条件 / 但 / 这个 / while / 循环 / 存在 / 一个 / 问题 / ： / 没有 / 定义 / 退出 / 条件 / 。 / 在 / 请 / 用户 / 进行 / 一系列 / 输入 / 时 / ， / 该 / 在 / 什么 / 地方 / 提供 / 退出 / 途径 / 呢 / ？ / 我们 / 要 / 让 / 用户 / 能够 / 尽可能 / 容易 / 地 / 退出 / ， / 因此 / 在 / 每次 / 提示 / 用户 / 输入 / 时 / ， / 都 / 应 / 提供 / 退出 / 途径 / 。 / 使用 / break / 语句 / 可以 / 在 / 每次 / 提示 / 用户 / 输入 / 时 / 提供 / 退出 / 循环 / 的 / 简单 / 途径 / ： / ` / ` / ` / python / def / get / _ / formatted / _ / name / ( / first / _ / name / , / last / _ / name / ) / : / \" / \" / \" / 返回 / 规范 / 格式 / 的 / 姓名 / \" / \" / \" / full / _ / name / = / f / \" / { / first / _ / name / } / { / last / _ / name / } / \" / return / full / _ / name / . / title / ( / ) / while / True / : / print / ( / \" / \\ / nPlease / tell / me / your / name / : / \" / ) / print / ( / \" / ( / enter / ' / q / ' / at / any / time / to / quit / ) / \" / ) / f / _ / name / = / input / ( / \" / First / name / : / \" / ) / if / f / _ / name / = / = / ' / q / ' / : / break / l / _ / name / = / input / ( / \" / Last / name / : / \" / ) / if / l / _ / name / = / = / ' / q / ' / : / break / formatted / _ / name / = / get / _ / formatted / _ / name / ( / f / _ / name / , / l / _ / name / ) / print / ( / f / \" / \\ / nHello / , / { / formatted / _ / name / } / ! / \" / ) / ` / ` / ` / 我们 / 添加 / 了 / 一条 / 消息 / 来 / 告诉 / 用户 / 如何 / 退出 / 。 / 然后 / 在 / 每次 / 提示 / 用户 / 输入 / 时 / ， / 都 / 检查 / 他 / 输入 / 的 / 是否是 / 退出 / 值 / 。 / 如果 / 是 / ， / 就 / 退出 / 循环 / 。 / 现在 / ， / 这个 / 程序 / 将 / 不断 / 地 / 发出 / 问候 / ， / 直到 / 用户 / 输入 / 的 / 姓 / 或 / 名为 / ' / q / ' / ： / ` / ` / ` / Please / tell / me / your / name / : / ( / enter / ' / q / ' / at / any / time / to / quit / ) / First / name / : / eric / Last / name / : / matthes / Hello / , / Eric / Matthes / ! / Please / tell / me / your / name / : / ( / enter / ' / q / ' / at / any / time / to / quit / ) / First / name / : / q / ` / ` / ` / ## / 传递 / 列表 / 你 / 经常 / 会 / 发现 / ， / 向 / 函数 / 传递 / 列表 / 很 / 有用 / ， / 可能 / 是 / 名字 / 列表 / 、 / 数值 / 列表 / 或 / 更 / 复杂 / 的 / 对象 / 列表 / （ / 如 / 字典 / ） / 。 / 将 / 列表 / 传递 / 给 / 函数 / 后 / ， / 函数 / 就 / 能 / 直接 / 访问 / 其 / 内容 / 。 / 下面 / 使用 / 函数 / 来 / 提高 / 处理 / 列表 / 的 / 效率 / 。 / 假设 / 有 / 一个 / 用户 / 列表 / ， / 而 / 我们 / 要 / 向 / 其中 / 的 / 每个 / 用户 / 发出 / 问候 / 。 / 下面 / 的 / 示例 / 将 / 一个 / 名字 / 列表 / 传递 / 给 / 一个 / 名为 / greet / _ / users / ( / ) / 的 / 函数 / ， / 这个 / 函数 / 会 / 向 / 列表 / 中 / 的 / 每个 / 人 / 发出 / 问候 / ： / #### / greet / _ / users / . / py / 示例 / ` / ` / ` / python / def / greet / _ / users / ( / names / ) / : / \" / \" / \" / 向 / 列表 / 中 / 的 / 每个 / 用户 / 发出 / 简单 / 的 / 问候 / \" / \" / \" / for / name / in / names / : / msg / = / f / \" / Hello / , / { / name / . / title / ( / ) / } / ! / \" / print / ( / msg / ) / usernames / = / [ / ' / hannah / ' / , / ' / ty / ' / , / ' / margot / ' / ] / greet / _ / users / ( / usernames / ) / ` / ` / ` / 我们 / 将 / greet / _ / users / ( / ) / 定义 / 成 / 接受 / 一个 / 名字 / 列表 / ， / 并 / 将 / 其赋 / 给 / 形参 / names / 。 / 这个 / 函数 / 遍历 / 收到 / 的 / 列表 / ， / 并 / 对 / 其中 / 的 / 每个 / 用户 / 打印 / 一条 / 问候语 / 。 / 在 / 函数 / 外 / ， / 先 / 定义 / 一个 / 用户 / 列表 / usernames / ， / 再 / 调用 / greet / _ / users / ( / ) / 并 / 将 / 这个 / 列表 / 传递 / 给 / 它 / ： / ` / ` / ` / Hello / , / Hannah / ! / Hello / , / Ty / ! / Hello / , / Margot / ! / ` / ` / ` / 输出 / 完全符合 / 预期 / 。 / 每个 / 用户 / 都 / 看到 / 了 / 一条 / 个性化 / 的 / 问候语 / 。 / 每当 / 需要 / 问候 / 一组 / 用户 / 时 / ， / 都 / 可 / 调用 / 这个 / 函数 / 。 / ### / 在 / 函数 / 中 / 修改 / 列表 / 将 / 列表 / 传递 / 给 / 函数 / 后 / ， / 函数 / 就 / 可以 / 对 / 其 / 进行 / 修改 / 了 / 。 / 在 / 函数 / 中 / 对 / 这个 / 列表 / 所 / 做 / 的 / 任何 / 修改 / 都 / 是 / 永久 / 的 / ， / 这 / 让 / 你 / 能够 / 高效 / 地 / 处理 / 大量 / 数据 / 。 / 来看 / 一家 / 为 / 用户 / 提交 / 的 / 设计 / 制作 / 3D / 打印 / 模型 / 的 / 公司 / 。 / 需要 / 打印 / 的 / 设计 / 事先 / 存储 / 在 / 一个 / 列表 / 中 / ， / 打印 / 后 / 将 / 被 / 移到 / 另 / 一个 / 列表 / 中 / 。 / 下面 / 是 / 在 / 不 / 使用 / 函数 / 的 / 情况 / 下 / 模拟 / 这个 / 过程 / 的 / 代码 / ： / #### / printing / _ / models / . / py / 示例 / ` / ` / ` / python / # / 首先 / 创建 / 一个 / 列表 / ， / 其中 / 包含 / 一些 / 要 / 打印 / 的 / 设计 / unprinted / _ / designs / = / [ / ' / phone / case / ' / , / ' / robot / pendant / ' / , / ' / dodecahedron / ' / ] / completed / _ / models / = / [ / ] / # / 模拟 / 打印 / 每个 / 设计 / ， / 直到 / 没有 / 未 / 打印 / 的 / 设计 / 为止 / # / 打印 / 每个 / 设计 / 后 / ， / 都 / 将 / 其移 / 到 / 列表 / completed / _ / models / 中 / while / unprinted / _ / designs / : / current / _ / design / = / unprinted / _ / designs / . / pop / ( / ) / print / ( / f / \" / Printing / model / : / { / current / _ / design / } / \" / ) / completed / _ / models / . / append / ( / current / _ / design / ) / # / 显示 / 打印 / 好 / 的 / 所有 / 模型 / print / ( / \" / \\ / nThe / following / models / have / been / printed / : / \" / ) / for / completed / _ / model / in / completed / _ / models / : / print / ( / completed / _ / model / ) / ` / ` / ` / 这个 / 程序 / 首先 / 创建 / 一个 / 需要 / 打印 / 的 / 设计 / 列表 / ， / 以及 / 一个 / 名为 / completed / _ / models / 的 / 空 / 列表 / ， / 打印 / 每个 / 设计 / 后 / 都 / 将 / 其移 / 到 / 这个 / 空 / 列表 / 中 / 。 / 只要 / 列表 / unprinted / _ / designs / 中 / 还有 / 设计 / ， / while / 循环 / 就 / 模拟 / 打印 / 设计 / 的 / 过程 / ： / 从 / 该 / 列表 / 末尾 / 删除 / 一个 / 设计 / ， / 将 / 其赋 / 给 / 变量 / current / _ / design / ， / 并 / 显示 / 一条 / 消息 / ， / 指出 / 正在 / 打印 / 当前 / 的 / 设计 / ， / 再 / 将 / 该 / 设计 / 加入 / 列表 / completed / _ / models / 。 / 循环 / 结束 / 后 / ， / 显示 / 已 / 打印 / 的 / 所有 / 设计 / ： / ` / ` / ` / Printing / model / : / dodecahedron / Printing / model / : / robot / pendant / Printing / model / : / phone / case / The / following / models / have / been / printed / : / dodecahedron / robot / pendant / phone / case / ` / ` / ` / #### / 使用 / 函数 / 重新 / 组织 / 代码 / 可以 / 重新 / 组织 / 这些 / 代码 / ， / 编写 / 两个 / 函数 / ， / 让 / 每个 / 都 / 做 / 一件 / 具体 / 的 / 工作 / 。 / 大部分 / 代码 / 与 / 原来 / 相同 / ， / 只是 / 结构 / 更为 / 合理 / 。 / 第一个 / 函数 / 负责 / 处理 / 打印 / 设计 / 的 / 工作 / ， / 第二个 / 概述 / 打印 / 了 / 哪些 / 设计 / ： / ` / ` / ` / python / def / print / _ / models / ( / unprinted / _ / designs / , / completed / _ / models / ) / : / \" / \" / \" / 模拟 / 打印 / 每个 / 设计 / ， / 直到 / 没有 / 未 / 打印 / 的 / 设计 / 为止 / 打印 / 每个 / 设计 / 后 / ， / 都 / 将 / 其移 / 到 / 列表 / completed / _ / models / 中 / \" / \" / \" / while / unprinted / _ / designs / : / current / _ / design / = / unprinted / _ / designs / . / pop / ( / ) / print / ( / f / \" / Printing / model / : / { / current / _ / design / } / \" / ) / completed / _ / models / . / append / ( / current / _ / design / ) / def / show / _ / completed / _ / models / ( / completed / _ / models / ) / : / \" / \" / \" / 显示 / 打印 / 好 / 的 / 所有 / 模型 / \" / \" / \" / print / ( / \" / \\ / nThe / following / models / have / been / printed / : / \" / ) / for / completed / _ / model / in / completed / _ / models / : / print / ( / completed / _ / model / ) / unprinted / _ / designs / = / [ / ' / phone / case / ' / , / ' / robot / pendant / ' / , / ' / dodecahedron / ' / ] / completed / _ / models / = / [ / ] / print / _ / models / ( / unprinted / _ / designs / , / completed / _ / models / ) / show / _ / completed / _ / models / ( / completed / _ / models / ) / ` / ` / ` / 首先 / ， / 定义 / 函数 / print / _ / models / ( / ) / ， / 它 / 包含 / 两个 / 形参 / ： / 一个 / 需要 / 打印 / 的 / 设计 / 列表 / 和 / 一个 / 打印 / 好 / 的 / 模型 / 列表 / 。 / 给定 / 这 / 两个 / 列表 / ， / 这个 / 函数 / 模拟 / 打印 / 每个 / 设计 / 的 / 过程 / ： / 将 / 设计 / 逐个 / 从未 / 打印 / 的 / 设计 / 列表 / 中 / 取出 / ， / 并 / 加入 / 打印 / 好 / 的 / 模型 / 列表 / 。 / 然后 / ， / 定义 / 函数 / show / _ / completed / _ / models / ( / ) / ， / 它 / 包含 / 一个 / 形参 / ： / 打印 / 好 / 的 / 模型 / 列表 / 。 / 给定 / 这个 / 列表 / ， / 函数 / show / _ / completed / _ / models / ( / ) / 显示 / 打印 / 出来 / 的 / 每个 / 模型 / 的 / 名称 / 。 / 虽然 / 这个 / 程序 / 的 / 输出 / 与 / 未 / 使用 / 函数 / 的 / 版本 / 相同 / ， / 但是 / 代码 / 更 / 有条理 / 。 / 完成 / 大部分 / 工作 / 的 / 代码 / 被 / 移到 / 了 / 两个 / 函数 / 中 / ， / 让 / 主程序 / 很 / 容易 / 理解 / 。 / 只要 / 看看 / 主程序 / ， / 你 / 就 / 能 / 轻松 / 地 / 知道 / 这个 / 程序 / 的 / 功能 / ： / ` / ` / ` / python / unprinted / _ / designs / = / [ / ' / phone / case / ' / , / ' / robot / pendant / ' / , / ' / dodecahedron / ' / ] / completed / _ / models / = / [ / ] / print / _ / models / ( / unprinted / _ / designs / , / completed / _ / models / ) / show / _ / completed / _ / models / ( / completed / _ / models / ) / ` / ` / ` / 我们 / 创建 / 了 / 一个 / 未 / 打印 / 的 / 设计 / 列表 / ， / 以及 / 一个 / 空 / 列表 / ， / 后者 / 用于 / 存储 / 打印 / 好 / 的 / 模型 / 。 / 接下来 / ， / 由于 / 已经 / 定义 / 了 / 两个 / 函数 / ， / 因此 / 只 / 需要 / 调用 / 它们 / 并 / 传入 / 正确 / 的 / 实参 / 即可 / 。 / 我们 / 调用 / print / _ / models / ( / ) / 并 / 向 / 它 / 传递 / 两个 / 列表 / 。 / 像 / 预期 / 的 / 一样 / ， / print / _ / models / ( / ) / 模拟 / 了 / 打印 / 设计 / 的 / 过程 / 。 / 接下来 / ， / 调用 / show / _ / completed / _ / models / ( / ) / ， / 并 / 将 / 打印 / 好 / 的 / 模型 / 列表 / 传递 / 给 / 它 / ， / 让 / 它 / 能够 / 指出 / 打印 / 了 / 哪些 / 模型 / 。 / 描述性 / 的 / 函数 / 名 / 让 / 阅读 / 这些 / 代码 / 的 / 人 / 也 / 能 / 一目了然 / ， / 虽然 / 其中 / 没有 / 任何 / 注释 / 。 / 相比 / 于 / 没有 / 使用 / 函数 / 的 / 版本 / ， / 这个 / 程序 / 更 / 容易 / 扩展 / 和 / 维护 / 。 / 如果 / 以后 / 需要 / 打印 / 其他 / 设计 / ， / 只 / 需 / 再次 / 调用 / print / _ / models / ( / ) / 即可 / 。 / 如果 / 发现 / 需要 / 对 / 模拟 / 打印 / 的 / 代码 / 进行 / 修改 / ， / 只 / 需 / 修改 / 这些 / 代码 / 一次 / ， / 就 / 将 / 影响 / 所有 / 调用 / 该 / 函数 / 的 / 地方 / 。 / 与 / 必须 / 分别 / 修改 / 程序 / 的 / 多个 / 地方 / 相比 / ， / 这种 / 修改 / 的 / 效率 / 更高 / 。 / 这个 / 程序 / 还 / 演示 / 了 / 一种 / 理念 / ： / 每个 / 函数 / 都 / 应 / 只 / 负责 / 一项 / 具体 / 工作 / 。 / 用 / 第一个 / 函数 / 打印 / 每个 / 设计 / ， / 用 / 第二个 / 函数 / 显示 / 打印 / 好 / 的 / 模型 / ， / 优于 / 使用 / 一个 / 函数 / 完成 / 这两项 / 工作 / 。 / 在 / 编写 / 函数 / 时 / ， / 如果 / 发现 / 它 / 执行 / 的 / 任务 / 太 / 多 / ， / 请 / 尝试 / 将 / 这些 / 代码 / 划分 / 到 / 两个 / 函数 / 中 / 。 / 别忘了 / ， / 总是 / 可以 / 在 / 一个 / 函数 / 中 / 调用 / 另 / 一个 / 函数 / ， / 这 / 有助于 / 将 / 复杂 / 的 / 任务 / 分解成 / 一系列 / 步骤 / 。 / ### / 禁止 / 函数 / 修改 / 列表 / 有时候 / ， / 需要 / 禁止 / 函数 / 修改 / 列表 / 。 / 假设 / 像 / 前 / 一个 / 示例 / 那样 / ， / 你 / 有 / 一个 / 未 / 打印 / 的 / 设计 / 列表 / ， / 并且 / 编写 / 了 / 一个 / 将 / 这些 / 设计 / 移 / 到 / 打印 / 好 / 的 / 模型 / 列表 / 中 / 的 / 函数 / 。 / 你 / 可能 / 会 / 做出 / 这样 / 的 / 决定 / ： / 即便 / 打印 / 了 / 所有 / 的 / 设计 / ， / 也 / 要 / 保留 / 原来 / 的 / 未 / 打印 / 的 / 设计 / 列表 / ， / 作为 / 存档 / 。 / 但 / 由于 / 你 / 将 / 所有 / 的 / 设计 / 都 / 移出 / 了 / unprinted / _ / designs / ， / 这个 / 列表 / 变成 / 了 / 空 / 的 / — / — / 原来 / 的 / 列表 / 没有 / 了 / 。 / 为了 / 解决 / 这个 / 问题 / ， / 可 / 向 / 函数 / 传递 / 列表 / 的 / 副本 / 而 / 不是 / 原始 / 列表 / 。 / 这样 / ， / 函数 / 所 / 做 / 的 / 任何 / 修改 / 都 / 只 / 影响 / 副本 / ， / 而 / 丝毫 / 不 / 影响 / 原始 / 列表 / 。 / #### / 传递 / 列表 / 副本 / 的 / 方法 / 要 / 将 / 列表 / 的 / 副本 / 传递 / 给 / 函数 / ， / 可以 / 像 / 下面 / 这样 / 做 / ： / ` / ` / ` / python / function / _ / name / ( / list / _ / name / [ / : / ] / ) / ` / ` / ` / 切片 / 表示法 / [ / : / ] / 创建 / 列表 / 的 / 副本 / 。 / 在 / printing / _ / models / . / py / 中 / ， / 如果 / 不想 / 清空 / 未 / 打印 / 的 / 设计 / 列表 / ， / 可像 / 下面 / 这样 / 调用 / print / _ / models / ( / ) / ： / ` / ` / ` / python / print / _ / models / ( / unprinted / _ / designs / [ / : / ] / , / completed / _ / models / ) / ` / ` / ` / print / _ / models / ( / ) / 函数 / 依然 / 能够 / 完成 / 其 / 工作 / ， / 因为 / 它 / 获得 / 了 / 所有 / 未 / 打印 / 的 / 设计 / 的 / 名称 / ， / 但 / 它 / 这次 / 使用 / 的 / 是 / 列表 / unprinted / _ / designs / 的 / 副本 / ， / 而 / 不是 / 列表 / unprinted / _ / designs / 本身 / 。 / 像 / 以前 / 一样 / ， / 列表 / completed / _ / models / 将 / 包含 / 打印 / 好 / 的 / 模型 / 的 / 名称 / ， / 但 / 函数 / 所 / 做 / 的 / 修改 / 不会 / 影响 / 列表 / unprinted / _ / designs / 。 / 虽然 / 向 / 函数 / 传递 / 列表 / 的 / 副本 / 可 / 保留 / 原始 / 列表 / 的 / 内容 / ， / 但 / 除非 / 有 / 充分 / 的 / 理由 / ， / 否则 / 还是 / 应该 / 将 / 原始 / 列表 / 传递 / 给 / 函数 / 。 / 这 / 是因为 / ， / 让 / 函数 / 使用 / 现成 / 的 / 列表 / 可 / 避免 / 花 / 时间 / 和 / 内存 / 创建 / 副本 / ， / 从而 / 提高效率 / ， / 在 / 处理 / 大型 / 列表 / 时 / 尤其 / 如此 / 。 / ## / 传递 / 任意 / 数量 / 的 / 实参 / 有时候 / ， / 你 / 预先 / 不 / 知道 / 函数 / 需要 / 接受 / 多少 / 个 / 实参 / ， / 好 / 在 / Python / 允许 / 函数 / 从 / 调用 / 语句 / 中 / 收集 / 任意 / 数量 / 的 / 实参 / 。 / 例如 / 一个 / 制作 / 比萨 / 的 / 函数 / ， / 它 / 需要 / 接受 / 很多 / 配料 / ， / 但 / 无法 / 预先确定 / 顾客 / 要点 / 多少 / 种 / 配料 / 。 / 下面 / 的 / 函数 / 只有 / 一个 / 形参 / \\ / * / toppings / ， / 不管 / 调用 / 语句 / 提供 / 了 / 多少 / 实参 / ， / 这个 / 形参 / 都 / 会 / 将 / 其 / 收入 / 囊中 / ： / #### / pizza / . / py / 示例 / ` / ` / ` / python / def / make / _ / pizza / ( / * / toppings / ) / : / \" / \" / \" / 打印 / 顾客 / 点 / 的 / 所有 / 配料 / \" / \" / \" / print / ( / toppings / ) / make / _ / pizza / ( / ' / pepperoni / ' / ) / make / _ / pizza / ( / ' / mushrooms / ' / , / ' / green / peppers / ' / , / ' / extra / cheese / ' / ) / ` / ` / ` / 形参名 / \\ / * / toppings / 中 / 的 / 星号 / 让 / Python / 创建 / 一个 / 名为 / toppings / 的 / 元组 / ， / 该 / 元组 / 包含 / 函数 / 收到 / 的 / 所有 / 值 / 。 / 函数 / 体内 / 的 / 函数调用 / print / ( / ) / 生成 / 的 / 输出 / 证明 / ， / Python / 既 / 能 / 处理 / 使用 / 一个 / 值 / 调用函数 / 的 / 情形 / ， / 也 / 能 / 处理 / 使用 / 三个 / 值 / 调用函数 / 的 / 情形 / 。 / 它 / 以 / 类似 / 的 / 方式 / 处理 / 不同 / 的 / 调用 / 。 / 注意 / ， / Python / 会 / 将 / 实参 / 封装 / 到 / 一个 / 元组 / 中 / ， / 即便 / 函数 / 只 / 收到 / 一个 / 值 / 也 / 是 / 如此 / ： / ` / ` / ` / ( / ' / pepperoni / ' / , / ) / ( / ' / mushrooms / ' / , / ' / green / peppers / ' / , / ' / extra / cheese / ' / ) / ` / ` / ` / 现在 / ， / 可以 / 将 / 函数调用 / print / ( / ) / 替换 / 为 / 一个 / 循环 / ， / 遍历 / 配料 / 列表 / 并 / 对 / 顾客 / 点 / 的 / 比萨 / 进行 / 描述 / ： / ` / ` / ` / python / def / make / _ / pizza / ( / * / toppings / ) / : / \" / \" / \" / 概述 / 要 / 制作 / 的 / 比萨 / \" / \" / \" / print / ( / \" / \\ / nMaking / a / pizza / with / the / following / toppings / : / \" / ) / for / topping / in / toppings / : / print / ( / f / \" / - / { / topping / } / \" / ) / make / _ / pizza / ( / ' / pepperoni / ' / ) / make / _ / pizza / ( / ' / mushrooms / ' / , / ' / green / peppers / ' / , / ' / extra / cheese / ' / ) / ` / ` / ` / 不管 / 收到 / 一个 / 值 / 还是 / 三个 / 值 / ， / 这个 / 函数 / 都 / 能 / 妥善 / 地 / 处理 / ： / ` / ` / ` / Making / a / pizza / with / the / following / toppings / : / - / pepperoni / Making / a / pizza / with / the / following / toppings / : / - / mushrooms / - / green / peppers / - / extra / cheese / ` / ` / ` / 不管 / 函数 / 收到 / 多少 / 个 / 实参 / ， / 这种 / 语法 / 都 / 管用 / 。 / ### / 结合 / 使用 / 位置 / 实参 / 和 / 任意 / 数量 / 的 / 实参 / 如果 / 要 / 让 / 函数 / 接受 / 不同 / 类型 / 的 / 实参 / ， / 必须 / 在 / 函数 / 定义 / 中将 / 接纳 / 任意 / 数量 / 实参 / 的 / 形参 / 放在 / 最后 / 。 / Python / 先 / 匹配 / 位置 / 实参 / 和 / 关键字 / 实参 / ， / 再 / 将 / 余下 / 的 / 实参 / 都 / 收集 / 到 / 最后 / 一个 / 形参中 / 。 / 例如 / ， / 如果 / 前面 / 的 / 函数 / 还 / 需要 / 一个 / 表示 / 比萨 / 尺寸 / 的 / 形参 / ， / 必须 / 将 / 该 / 形参 / 放在 / 形参 / \\ / * / toppings / 的 / 前面 / ： / ` / ` / ` / python / def / make / _ / pizza / ( / size / , / * / toppings / ) / : / \" / \" / \" / 概述 / 要 / 制作 / 的 / 比萨 / \" / \" / \" / print / ( / f / \" / \\ / nMaking / a / { / size / } / - / inch / pizza / with / the / following / toppings / : / \" / ) / for / topping / in / toppings / : / print / ( / f / \" / - / { / topping / } / \" / ) / make / _ / pizza / ( / 16 / , / ' / pepperoni / ' / ) / make / _ / pizza / ( / 12 / , / ' / mushrooms / ' / , / ' / green / peppers / ' / , / ' / extra / cheese / ' / ) / ` / ` / ` / 基于 / 上述 / 函数 / 定义 / ， / Python / 将 / 收到 / 的 / 第一个 / 值赋 / 给 / 形参 / size / ， / 将 / 其他 / 所有 / 的 / 值 / 都 / 存储 / 在 / 元组 / toppings / 中 / 。 / 在 / 函数调用 / 中 / ， / 首先 / 指定 / 表示 / 比萨 / 尺寸 / 的 / 实参 / ， / 再 / 根据 / 需要 / 指定 / 任意 / 数量 / 的 / 配料 / 。 / 现在 / ， / 每个 / 比萨 / 都 / 有 / 了 / 尺寸 / 和 / 一系列 / 配料 / ， / 而且 / 这些 / 信息 / 被 / 按 / 正确 / 的 / 顺序 / 打印 / 出来 / 了 / — / — / 首先 / 是 / 尺寸 / ， / 然后 / 是 / 配料 / ： / ` / ` / ` / Making / a / 16 / - / inch / pizza / with / the / following / toppings / : / - / pepperoni / Making / a / 12 / - / inch / pizza / with / the / following / toppings / : / - / mushrooms / - / green / peppers / - / extra / cheese / ` / ` / ` / 注意 / ： / 你 / 经常 / 会 / 看到 / 通用 / 形参名 / \\ / * / args / ， / 它 / 也 / 这样 / 收集 / 任意 / 数量 / 的 / 位置 / 实参 / 。 / ### / 使用 / 任意 / 数量 / 的 / 关键字 / 实参 / 有时候 / ， / 你 / 需要 / 接受 / 任意 / 数量 / 的 / 实参 / ， / 但 / 预先 / 不 / 知道 / 传递 / 给 / 函数 / 的 / 会 / 是 / 什么样 / 的 / 信息 / 。 / 在 / 这种 / 情况 / 下 / ， / 可 / 将 / 函数 / 编写成 / 能够 / 接受 / 任意 / 数量 / 的 / 键值 / 对 / — / — / 调用 / 语句 / 提供 / 了 / 多少 / 就 / 接受 / 多少 / 。 / 一个 / 这样 / 的 / 示例 / 是 / 创建 / 用户 / 简介 / ： / 你 / 知道 / 将 / 收到 / 有关 / 用户 / 的 / 信息 / ， / 但 / 不 / 确定 / 是 / 什么样 / 的 / 信息 / 。 / 在 / 下面 / 的 / 示例 / 中 / ， / build / _ / profile / ( / ) / 函数 / 不仅 / 接受 / 名和姓 / ， / 还 / 接受 / 任意 / 数量 / 的 / 关键字 / 实参 / ： / #### / user / _ / profile / . / py / 示例 / ` / ` / ` / python / def / build / _ / profile / ( / first / , / last / , / * / * / user / _ / info / ) / : / \" / \" / \" / 创建 / 一个 / 字典 / ， / 其中 / 包含 / 我们 / 知道 / 的 / 有关 / 用户 / 的 / 一切 / \" / \" / \" / user / _ / info / [ / ' / first / _ / name / ' / ] / = / first / user / _ / info / [ / ' / last / _ / name / ' / ] / = / last / return / user / _ / info / user / _ / profile / = / build / _ / profile / ( / ' / albert / ' / , / ' / einstein / ' / , / location / = / ' / princeton / ' / , / field / = / ' / physics / ' / ) / print / ( / user / _ / profile / ) / ` / ` / ` / build / _ / profile / ( / ) / 函数 / 的 / 定义 / 要求 / 提供 / 名和姓 / ， / 同时 / 允许 / 根据 / 需要 / 提供 / 任意 / 数量 / 的 / 名值 / 对 / 。 / 形参 / \\ / * / \\ / * / user / _ / info / 中 / 的 / 两个 / 星号 / 让 / Python / 创建 / 一个 / 名为 / user / _ / info / 的 / 字典 / ， / 该 / 字典 / 包含 / 函数 / 收到 / 的 / 其他 / 所有 / 名值 / 对 / 。 / 在 / 这个 / 函数 / 中 / ， / 可以 / 像 / 访问 / 其他 / 字典 / 那样 / 访问 / user / _ / info / 中 / 的 / 名值 / 对 / 。 / 在 / build / _ / profile / ( / ) / 的 / 函数 / 体内 / ， / 将名 / 和 / 姓 / 加入 / 字典 / user / _ / info / ， / 因为 / 总是 / 会 / 从 / 用户 / 那里 / 收到 / 这两项 / 信息 / ， / 而 / 这两项 / 信息 / 还 / 没 / 被 / 放在 / 字典 / 中 / 。 / 接下来 / ， / 将 / 字典 / user / _ / info / 返回 / 函数调用 / 行 / 。 / 我们 / 调用 / build / _ / profile / ( / ) / ， / 向 / 它 / 传递 / 名 / （ / ' / albert / ' / ） / 、 / 姓 / （ / ' / einstein / ' / ） / 和 / 两个 / 键值 / 对 / （ / location / = / ' / princeton / ' / 和 / field / = / ' / physics / ' / ） / ， / 并 / 将 / 返回 / 的 / user / _ / info / 赋给 / 变量 / user / _ / profile / ， / 再 / 打印 / 这个 / 变量 / ： / ` / ` / ` / { / ' / location / ' / : / ' / princeton / ' / , / ' / field / ' / : / ' / physics / ' / , / ' / first / _ / name / ' / : / ' / albert / ' / , / ' / last / _ / name / ' / : / ' / einstein / ' / } / ` / ` / ` / 在 / 这里 / ， / 返回 / 的 / 字典 / 包含 / 用户 / 的 / 名 / 和 / 姓 / ， / 还有 / 居住地 / 和 / 研究 / 领域 / 。 / 在 / 调用 / 这个 / 函数 / 时 / ， / 不管 / 额外 / 提供 / 多少 / 个 / 键值 / 对 / ， / 它 / 都 / 能 / 正确 / 地 / 处理 / 。 / 在 / 编写 / 函数 / 时 / ， / 可以 / 用 / 各种 / 方式 / 混合 / 使用 / 位置 / 实参 / 、 / 关键字 / 实参 / 和 / 任意 / 数量 / 的 / 实参 / 。 / 知道 / 这些 / 实参 / 类型 / 大有裨益 / ， / 因为 / 你 / 在 / 阅读 / 别人 / 编写 / 的 / 代码 / 时 / 经常 / 会 / 见到 / 它们 / 。 / 要 / 正确 / 地 / 使用 / 这些 / 类型 / 的 / 实参 / 并 / 知道 / 使用 / 它们 / 的 / 时机 / ， / 需要 / 一定 / 的 / 练习 / 。 / 就 / 目前 / 而言 / ， / 牢记 / 使用 / 最 / 简单 / 的 / 方法 / 来 / 完成 / 任务 / 就 / 好 / 了 / 。 / 继续 / 往 / 下 / 阅读 / ， / 你 / 就 / 会 / 知道 / 在 / 各种 / 情况 / 下 / 使用 / 哪 / 种 / 方法 / 的 / 效率 / 最高 / 。 / 注意 / ： / 你 / 经常 / 会 / 看到 / 形参名 / \\ / * / \\ / * / kwargs / ， / 它 / 用于 / 收集 / 任意 / 数量 / 的 / 关键字 / 实参 / 。 / ## / 将 / 函数 / 存储 / 在 / 模块 / 中 / 使用 / 函数 / 的 / 优点 / 之一 / 是 / 可 / 将 / 代码 / 块 / 与 / 主程序 / 分离 / 。 / 通过 / 给 / 函数 / 指定 / 描述性 / 名称 / ， / 能 / 让 / 程序 / 容易 / 理解 / 得 / 多 / 。 / 你 / 还 / 可以 / 更进一步 / ， / 将 / 函数 / 存储 / 在 / 称为 / * / * / 模块 / * / * / 的 / 独立 / 文件 / 中 / ， / 再 / 将 / 模块 / 导入 / （ / import / ） / 主程序 / 。 / import / 语句 / 可 / 让你在 / 当前 / 运行 / 的 / 程序 / 文件 / 中 / 使用 / 模块 / 中 / 的 / 代码 / 。 / 通过 / 将 / 函数 / 存储 / 在 / 独立 / 的 / 文件 / 中 / ， / 可 / 隐藏 / 程序代码 / 的 / 细节 / ， / 将 / 重点 / 放在 / 程序 / 的 / 高层 / 逻辑 / 上 / 。 / 这 / 还 / 能 / 让你在 / 众多 / 不同 / 的 / 程序 / 中 / 复用 / 函数 / 。 / 将 / 函数 / 存储 / 在 / 独立 / 文件 / 中后 / ， / 可 / 与 / 其他 / 程序员 / 共享 / 这些 / 文件 / 而 / 不是 / 整个 / 程序 / 。 / 知道 / 如何 / 导入 / 函数 / 还 / 能 / 让 / 你 / 使用 / 其他 / 程序员 / 编写 / 的 / 函数库 / 。 / 导入 / 模块 / 的 / 方法 / 有 / 好几种 / ， / 下面 / 对 / 每种 / 都 / 做 / 简要 / 的 / 介绍 / 。 / ### / 导入 / 整个 / 模块 / 要 / 让 / 函数 / 是 / 可 / 导入 / 的 / ， / 得 / 先 / 创建 / 模块 / 。 / * / * / 模块 / * / * / 是 / 扩展 / 名为 / . / py / 的 / 文件 / ， / 包含 / 要 / 导入 / 程序 / 的 / 代码 / 。 / 下面 / 来 / 创建 / 一个 / 包含 / make / _ / pizza / ( / ) / 函数 / 的 / 模块 / 。 / 为此 / ， / 将 / 文件 / pizza / . / py / 中 / 除了 / 函数 / make / _ / pizza / ( / ) / 之外 / 的 / 代码 / 删除 / ： / #### / pizza / . / py / 模块 / ` / ` / ` / python / def / make / _ / pizza / ( / size / , / * / toppings / ) / : / \" / \" / \" / 概述 / 要 / 制作 / 的 / 比萨 / \" / \" / \" / print / ( / f / \" / \\ / nMaking / a / { / size / } / - / inch / pizza / with / the / following / toppings / : / \" / ) / for / topping / in / toppings / : / print / ( / f / \" / - / { / topping / } / \" / ) / ` / ` / ` / 接下来 / ， / 在 / pizza / . / py / 所在 / 的 / 目录 / 中 / 创建 / 一个 / 名为 / making / _ / pizzas / . / py / 的 / 文件 / 。 / 这个 / 文件 / 先导 / 入刚 / 创建 / 的 / 模块 / ， / 再 / 调用 / make / _ / pizza / ( / ) / 两次 / ： / #### / making / _ / pizzas / . / py / 示例 / ` / ` / ` / python / import / pizza / pizza / . / make / _ / pizza / ( / 16 / , / ' / pepperoni / ' / ) / pizza / . / make / _ / pizza / ( / 12 / , / ' / mushrooms / ' / , / ' / green / peppers / ' / , / ' / extra / cheese / ' / ) / ` / ` / ` / 当 / Python / 读取 / 这个 / 文件 / 时 / ， / 代码 / 行 / import / pizza / 会 / 让 / Python / 打开 / 文件 / pizza / . / py / ， / 并 / 将 / 其中 / 的 / 所有 / 函数 / 都 / 复制到 / 这个 / 程序 / 中 / 。 / 你 / 看不到 / 复制 / 代码 / 的 / 过程 / ， / 因为 / Python / 会 / 在 / 程序 / 即将 / 运行 / 时 / 在 / 幕后 / 复制 / 这些 / 代码 / 。 / 你 / 只 / 需要 / 知道 / ， / 在 / making / _ / pizzas / . / py / 中 / ， / 可 / 使用 / pizza / . / py / 中 / 定义 / 的 / 所有 / 函数 / 。 / 要 / 调用 / 被 / 导入 / 模块 / 中 / 的 / 函数 / ， / 可 / 指定 / 被 / 导入 / 模块 / 的 / 名称 / pizza / 和 / 函数 / 名 / make / _ / pizza / ( / ) / ， / 并用 / 句点 / 隔开 / 。 / 这些 / 代码 / 的 / 输出 / 与 / 没有 / 导入 / 模块 / 的 / 原始 / 程序 / 相同 / ： / ` / ` / ` / Making / a / 16 / - / inch / pizza / with / the / following / toppings / : / - / pepperoni / Making / a / 12 / - / inch / pizza / with / the / following / toppings / : / - / mushrooms / - / green / peppers / - / extra / cheese / ` / ` / ` / 这 / 就是 / 一种 / 导入 / 方法 / ： / 只 / 需 / 编写 / 一条 / import / 语句 / 并 / 在 / 其中 / 指定 / 模块 / 名 / ， / 就 / 可 / 在 / 程序 / 中 / 使用 / 该 / 模块 / 中 / 的 / 所有 / 函数 / 。 / 如果 / 使用 / 这种 / import / 语句 / 导入 / 了 / 名为 / module / _ / name / . / py / 的 / 整个 / 模块 / ， / 就 / 可 / 使用 / 下面 / 的 / 语法 / 来 / 使用 / 其中 / 的 / 任意 / 一个 / 函数 / ： / ` / ` / ` / python / module / _ / name / . / function / _ / name / ( / ) / ` / ` / ` / ### / 导入 / 特定 / 的 / 函数 / 还 / 可以 / 只 / 导入 / 模块 / 中 / 的 / 特定 / 函数 / ， / 语法 / 如下 / ： / ` / ` / ` / python / from / module / _ / name / import / function / _ / name / ` / ` / ` / 用 / 逗号 / 分隔 / 函数 / 名 / ， / 可 / 根据 / 需要 / 从 / 模块 / 中 / 导入 / 任意 / 数量 / 的 / 函数 / ： / ` / ` / ` / python / from / module / _ / name / import / function / _ / 0 / , / function / _ / 1 / , / function / _ / 2 / ` / ` / ` / 对于 / 前面 / 的 / making / _ / pizzas / . / py / 示例 / ， / 如果 / 只想 / 导入 / 要 / 使用 / 的 / 函数 / ， / 代码 / 将 / 类似 / 于 / 下面 / 这样 / ： / ` / ` / ` / python / from / pizza / import / make / _ / pizza / make / _ / pizza / ( / 16 / , / ' / pepperoni / ' / ) / make / _ / pizza / ( / 12 / , / ' / mushrooms / ' / , / ' / green / peppers / ' / , / ' / extra / cheese / ' / ) / ` / ` / ` / 如果 / 使用 / 这种 / 语法 / ， / 在 / 调用函数 / 时则 / 无须 / 使用 / 句点 / 。 / 由于 / 在 / import / 语句 / 中显式 / 地 / 导入 / 了 / make / _ / pizza / ( / ) / 函数 / ， / 因此 / 在 / 调用 / 时 / 只 / 需 / 指定 / 其 / 名称 / 即可 / 。 / ### / 使用 / as / 给 / 函数 / 指定 / 别名 / 如果 / 要 / 导入 / 的 / 函数 / 的 / 名称 / 太 / 长 / 或者 / 可能 / 与 / 程序 / 中 / 既有 / 的 / 名称 / 冲突 / ， / 可 / 指定 / 简短 / 而 / 独一无二 / 的 / * / * / 别名 / * / * / （ / alias / ） / ： / 函数 / 的 / 另 / 一个 / 名称 / ， / 类似 / 于 / 外号 / 。 / 要 / 给 / 函数 / 指定 / 这种 / 特殊 / 的 / 外号 / ， / 需要 / 在 / 导入 / 时 / 这样 / 做 / 。 / 下面 / 给 / make / _ / pizza / ( / ) / 函数 / 指定 / 了 / 别名 / mp / ( / ) / 。 / 这 / 是 / 在 / import / 语句 / 中 / 使用 / make / _ / pizza / as / mp / 实现 / 的 / ， / 关键字 / as / 将 / 函数 / 重命名 / 为 / 指定 / 的 / 别名 / ： / ` / ` / ` / python / from / pizza / import / make / _ / pizza / as / mp / mp / ( / 16 / , / ' / pepperoni / ' / ) / mp / ( / 12 / , / ' / mushrooms / ' / , / ' / green / peppers / ' / , / ' / extra / cheese / ' / ) / ` / ` / ` / 上面 / 的 / import / 语句 / 将 / 函数 / make / _ / pizza / ( / ) / 重命名 / 为 / mp / ( / ) / 。 / 在 / 这个 / 程序 / 中 / ， / 每当 / 需要 / 调用 / make / _ / pizza / ( / ) / 时 / ， / 都 / 可 / 将 / 其 / 简写 / 成 / mp / ( / ) / 。 / Python / 将 / 运行 / make / _ / pizza / ( / ) / 中 / 的 / 代码 / ， / 同时 / 避免 / 与 / 程序 / 可能 / 包含 / 的 / make / _ / pizza / ( / ) / 函数 / 混淆 / 。 / 指定 / 别名 / 的 / 通用 / 语法 / 如下 / ： / ` / ` / ` / python / from / module / _ / name / import / function / _ / name / as / fn / ` / ` / ` / ### / 使用 / as / 给 / 模块 / 指定 / 别名 / 还 / 可以 / 给 / 模块 / 指定 / 别名 / 。 / 通过 / 给 / 模块 / 指定 / 简短 / 的 / 别名 / （ / 如给 / pizza / 模块 / 指定 / 别名 / p / ） / ， / 你 / 能够 / 更 / 轻松 / 地 / 调用 / 模块 / 中 / 的 / 函数 / 。 / 相比 / 于 / pizza / . / make / _ / pizza / ( / ) / ， / p / . / make / _ / pizza / ( / ) / 显然 / 更加 / 简洁 / ： / ` / ` / ` / python / import / pizza / as / p / p / . / make / _ / pizza / ( / 16 / , / ' / pepperoni / ' / ) / p / . / make / _ / pizza / ( / 12 / , / ' / mushrooms / ' / , / ' / green / peppers / ' / , / ' / extra / cheese / ' / ) / ` / ` / ` / 上述 / import / 语句 / 给 / pizza / 模块 / 指定 / 了 / 别名 / p / ， / 但 / 该 / 模块 / 中 / 所有 / 函数 / 的 / 名称 / 都 / 没变 / 。 / 要 / 调用 / make / _ / pizza / ( / ) / 函数 / ， / 可 / 将 / 其 / 写 / 为 / p / . / make / _ / pizza / ( / ) / 而 / 不是 / pizza / . / make / _ / pizza / ( / ) / 。 / 这样 / 不仅 / 让 / 代码 / 更加 / 简洁 / ， / 还 / 让 / 你 / 不用 / 再 / 关注 / 模块 / 名 / ， / 只 / 专注 / 于 / 描述性 / 的 / 函数 / 名 / 。 / 这些 / 函数 / 名 / 明确 / 地 / 指出 / 了 / 函数 / 的 / 功能 / ， / 对于 / 理解 / 代码 / 来说 / ， / 它们 / 比 / 模块 / 名 / 更 / 重要 / 。 / 给 / 模块 / 指定 / 别名 / 的 / 通用 / 语法 / 如下 / ： / ` / ` / ` / python / import / module / _ / name / as / mn / ` / ` / ` / ### / 导入 / 模块 / 中 / 的 / 所有 / 函数 / 使用 / 星号 / （ / \\ / * / ） / 运算符 / 可 / 让 / Python / 导入 / 模块 / 中 / 的 / 所有 / 函数 / ： / ` / ` / ` / python / from / pizza / import / * / make / _ / pizza / ( / 16 / , / ' / pepperoni / ' / ) / make / _ / pizza / ( / 12 / , / ' / mushrooms / ' / , / ' / green / peppers / ' / , / ' / extra / cheese / ' / ) / ` / ` / ` / import / 语句 / 中 / 的 / 星号 / 让 / Python / 将 / 模块 / pizza / 中 / 的 / 每个 / 函数 / 都 / 复制到 / 这个 / 程序 / 文件 / 中 / 。 / 由于 / 导入 / 了 / 每个 / 函数 / ， / 可 / 通过 / 名称 / 来 / 调用 / 每个 / 函数 / ， / 无须 / 使用 / * / * / 点 / 号 / * / * / （ / dot / notation / ） / 。 / 然而 / ， / 在 / 使用 / 并非 / 自己 / 编写 / 的 / 大型 / 模块 / 时 / ， / 最好 / 不要 / 使用 / 这种 / 导入 / 方法 / ， / 因为 / 如果 / 模块 / 中有 / 函数 / 的 / 名称 / 与 / 当前 / 项目 / 中 / 既有 / 的 / 名称 / 相同 / ， / 可能 / 导致 / 意想不到 / 的 / 结果 / ： / Python / 可能 / 会 / 因为 / 遇到 / 多个 / 名称 / 相同 / 的 / 函数 / 或 / 变量 / 而 / 覆盖 / 函数 / ， / 而 / 不是 / 分别 / 导入 / 所有 / 的 / 函数 / 。 / 最佳 / 的 / 做法 / 是 / ， / 要么 / 只 / 导入 / 需要 / 使用 / 的 / 函数 / ， / 要么 / 导入 / 整个 / 模块 / 并 / 使用 / 点 / 号 / 。 / 这 / 都 / 能 / 让 / 代码 / 更 / 清晰 / ， / 更 / 容易 / 阅读 / 和 / 理解 / 。 / 这里 / 之所以 / 介绍 / 导入 / 模块 / 中 / 所有 / 函数 / 的 / 方法 / ， / 只是 / 想 / 让你在 / 阅读 / 别人 / 编写 / 的 / 代码 / 时 / ， / 能够 / 理解 / 类似 / 于 / 下面 / 的 / import / 语句 / ： / ` / ` / ` / python / from / module / _ / name / import / * / ` / ` / ` / ## / 函数 / 编写 / 指南 / 在 / 编写 / 函数 / 时 / ， / 需要 / 牢记 / 几个 / 细节 / 。 / 应 / 给 / 函数 / 指定 / 描述性 / 名称 / ， / 且 / 只 / 使用 / 小写字母 / 和 / 下划线 / 。 / 描述性 / 名称 / 可 / 帮助 / 你 / 和 / 别人 / 明白 / 代码 / 想要 / 做 / 什么 / 。 / 在 / 给 / 模块 / 命名 / 时 / 也 / 应 / 遵循 / 上述 / 约定 / 。 / 每个 / 函数 / 都 / 应 / 包含 / 简要 / 阐述 / 其 / 功能 / 的 / 注释 / 。 / 该 / 注释 / 应 / 紧跟 / 在 / 函数 / 定义 / 后面 / ， / 并 / 采用 / 文档 / 字符串 / 的 / 格式 / 。 / 这样 / ， / 其他 / 程序员 / 只 / 需 / 阅读 / 文档 / 字符串 / 中 / 的 / 描述 / 就 / 能够 / 使用 / 它 / ： / 他们 / 完全 / 可以 / 相信 / 代码 / 会 / 如 / 描述 / 的 / 那样 / 运行 / ， / 并且 / 只要 / 知道 / 函数 / 名 / 、 / 需要 / 的 / 实参 / 以及 / 返回值 / 的 / 类型 / ， / 就 / 能 / 在 / 自己 / 的 / 程序 / 中 / 使用 / 它 / 。 / ### / 格式 / 规范 / 在 / 给 / 形参 / 指定 / 默认值 / 时 / ， / 等 / 号 / 两边 / 不要 / 有 / 空格 / ： / ` / ` / ` / python / def / function / _ / name / ( / parameter / _ / 0 / , / parameter / _ / 1 / = / ' / default / value / ' / ) / ` / ` / ` / 函数调用 / 中 / 的 / 关键字 / 实参 / 也 / 应 / 遵循 / 这种 / 约定 / ： / ` / ` / ` / python / function / _ / name / ( / value / _ / 0 / , / parameter / _ / 1 / = / ' / value / ' / ) / ` / ` / ` / PEP / 8 / 建议 / 代码 / 行 / 的 / 长度 / 不要 / 超过 / 79 / 个字符 / 。 / 这样 / ， / 只要 / 编辑器 / 窗口 / 适中 / ， / 就 / 能 / 看到 / 整行 / 代码 / 。 / 如果 / 形参 / 很多 / ， / 导致 / 函数 / 定义 / 的 / 长度 / 超过 / 了 / 79 / 个字符 / ， / 可 / 在 / 函数 / 定义 / 中 / 输入 / 左 / 括号 / 后 / 按 / 回车键 / ， / 并 / 在 / 下 / 一行 / 连 / 按 / 两次 / 制表符 / 键 / ， / 从而 / 将 / 形参 / 列表 / 和 / 只 / 缩进 / 一层 / 的 / 函数 / 体 / 区分 / 开来 / 。 / 大多数 / 编辑器 / 会 / 自动 / 对齐 / 后续 / 参数 / 列表 / 行 / ， / 使 / 其 / 缩进 / 程度 / 与 / 你 / 给 / 第一个 / 参数 / 列表 / 行 / 指定 / 的 / 缩进 / 程度 / 相同 / ： / ` / ` / ` / python / def / function / _ / name / ( / parameter / _ / 0 / , / parameter / _ / 1 / , / parameter / _ / 2 / , / parameter / _ / 3 / , / parameter / _ / 4 / , / parameter / _ / 5 / ) / : / function / body / ... / ` / ` / ` / 如果 / 程序 / 或 / 模块 / 包含 / 多个 / 函数 / ， / 可 / 使用 / 两个 / 空行 / 将 / 相邻 / 的 / 函数 / 分开 / 。 / 这样 / 将 / 更 / 容易 / 知道 / 前 / 一个 / 函数 / 到 / 什么 / 地方 / 结束 / ， / 下 / 一个 / 函数 / 从 / 什么 / 地方 / 开始 / 。 / 所有 / 的 / import / 语句 / 都 / 应 / 放在 / 文件 / 开头 / 。 / 唯一 / 的 / 例外 / 是 / ， / 你 / 要 / 在 / 文件 / 开头 / 使用 / 注释 / 来 / 描述 / 整个 / 程序 / 。 / ## / 小结 / 在 / 本章 / 中 / ， / 你 / 首先 / 学习 / 了 / 如何 / 编写 / 函数 / ， / 以及 / 如何 / 传递 / 实参 / ， / 让 / 函数 / 能够 / 访问 / 完成 / 工作 / 所 / 需 / 的 / 信息 / 。 / 然后 / 学习 / 了 / 如何 / 使用 / 位置 / 实参 / 和 / 关键字 / 实参 / ， / 以及 / 如何 / 接受 / 任意 / 数量 / 的 / 实参 / 。 / 你 / 见识 / 了 / 显示 / 输出 / 的 / 函数 / 和 / 返回值 / 的 / 函数 / ， / 知道 / 了 / 如何 / 将 / 函数 / 与 / 列表 / 、 / 字典 / 、 / if / 语句 / 和 / while / 循环 / 结合 / 起来 / 使用 / ， / 以及 / 如何 / 将 / 函数 / 存储 / 在 / 称为 / 模块 / 的 / 独立 / 文件 / 中 / ， / 让 / 程序 / 文件 / 更 / 简单 / 、 / 更 / 易于 / 理解 / 。 / 最后 / ， / 你 / 了解 / 了 / 函数 / 编写 / 指南 / ， / 遵循 / 这些 / 指南 / 可 / 让 / 程序 / 始终 / 保持良好 / 的 / 结构 / ， / 对 / 你 / 和 / 其他人 / 来说 / 都 / 易于 / 阅读 / 。 / 程序员 / 的 / 目标 / 之一 / 是 / 编写 / 简单 / 的 / 代码 / 来 / 完成 / 任务 / ， / 而 / 函数 / 有助于 / 实现 / 这样 / 的 / 目标 / 。 / 使用 / 它们 / ， / 你 / 在 / 编写 / 好 / 一个个 / 代码 / 块 / 并 / 确定 / 其 / 能够 / 正确 / 运行 / 后 / ， / 就 / 可 / 不必 / 在 / 上面 / 花 / 更 / 多 / 精力 / 。 / 确定 / 函数 / 能够 / 正确 / 地 / 完成 / 工作 / 后 / ， / 你 / 就 / 可以 / 接着 / 投身于 / 下 / 一个 / 编程 / 任务 / ， / 因为 / 你 / 知道 / 它们 / 以后 / 也 / 不会 / 出 / 问题 / 。 / 函数 / 让你在 / 编写 / 一次 / 代码 / 后 / ， / 可以 / 复用 / 它们 / 任意 / 多次 / 。 / 当 / 需要 / 运行 / 函数 / 中 / 的 / 代码 / 时 / ， / 只 / 需 / 编写 / 一行 / 函数调用 / 代码 / ， / 就 / 能 / 让 / 函数 / 完成 / 其 / 工作 / 。 / 当 / 需要 / 修改 / 函数 / 的 / 行为 / 时 / ， / 只 / 需 / 修改 / 一个 / 代码 / 块 / ， / 你 / 所 / 做 / 的 / 修改 / 就 / 将 / 影响 / 调用 / 这个 / 函数 / 的 / 每个 / 地方 / 。 / 使用 / 函数 / 让 / 程序 / 更 / 容易 / 阅读 / ， / 而 / 良好 / 的 / 函数 / 名 / 概述 / 了 / 程序 / 各个 / 部分 / 的 / 作用 / 。 / 相比 / 于 / 阅读 / 一系列 / 代码 / 块 / ， / 阅读 / 一系列 / 函数调用 / 让 / 你 / 能够 / 更快 / 地 / 明白 / 程序 / 的 / 作用 / 。 / 函数 / 还 / 让 / 代码 / 更 / 容易 / 测试 / 和 / 调试 / 。 / 如果 / 程序 / 使用 / 一系列 / 函数 / 来 / 完成 / 任务 / ， / 其中 / 的 / 每个 / 函数 / 都 / 完成 / 一项 / 具体 / 工作 / ， / 那么 / 程序 / 测试 / 和 / 维护 / 起来 / 将 / 容易 / 得 / 多 / ： / 可 / 编写 / 分别 / 调用 / 每个 / 函数 / 的 / 程序 / ， / 并 / 测试 / 每个 / 函数 / 是否 / 在 / 可能 / 的 / 各种 / 情形 / 下 / 都 / 能 / 正确 / 地 / 运行 / 。 / 经过 / 这样 / 的 / 测试 / ， / 你 / 就 / 能 / 深信 / 每次 / 调用 / 这些 / 函数 / 时 / ， / 它们 / 都 / 将 / 正确 / 地 / 运行 / 。 / 在 / 第 / 9 / 章中 / ， / 你 / 将 / 学习 / 编写 / 类 / 。 / 类 / 将 / 函数 / 和 / 数据 / 整洁地 / 封装 / 起来 / ， / 让 / 你 / 能够 / 灵活 / 而 / 高效 / 地 / 使用 / 它们 / 。\n", "------------------------------------------------------------\n", "\n", "📝 文档 2: 人工智能.txt\n", "原文字符数: 2621\n", "分词后词数: 1643\n", "分词结果预览 (前20个词): ['人工智能', '：', '未来', '的', '奇迹', '？', '灾难', '？', '随便', '聊聊', '人工智能', '，', '这个', '词', '我', '第一次', '听说', '是', '在', '高中']\n", "高频词 (前10个):\n", "  '，': 174次\n", "  '？': 61次\n", "  'AI': 61次\n", "  '。': 42次\n", "  '的': 39次\n", "  '我': 26次\n", "  '了': 25次\n", "  '是': 24次\n", "  '说': 24次\n", "  '人类': 20次\n", "\n", "完整分词结果:\n", "人工智能 / ： / 未来 / 的 / 奇迹 / ？ / 灾难 / ？ / 随便 / 聊聊 / 人工智能 / ， / 这个 / 词 / 我 / 第一次 / 听说 / 是 / 在 / 高中 / 吧 / ， / 那 / 时候 / 老师 / 说 / 未来 / 会 / 有 / 机器人 / 帮 / 我们 / 打扫卫生 / 、 / 帮 / 我们 / 写 / 作业 / ， / 我 / 当时 / 笑 / 了 / ， / 心想 / 这 / 哪 / 可能 / ？ / 结果 / 现在 / 扫地 / 机器人 / 满大街 / 跑 / ， / ChatGPT / 能 / 帮 / 你 / 写作文 / ， / 甚至 / 有些 / 学生 / 直接 / 用 / 它 / 写 / 论文 / ， / 还 / 被 / 老师 / 抓包 / 。 / 哎 / ， / 说 / 到 / 这个 / ， / 我 / 突然 / 想到 / 一个 / 问题 / ： / 如果 / 学生 / 都 / 用 / AI / 写 / 作业 / ， / 那 / 老师 / 还 / 怎么 / 判断 / 水平 / ？ / 是不是 / 干脆 / 让 / AI / 给 / AI / 打分 / ？ / 然后 / 我们 / 人类 / 就 / 只 / 需要 / 看 / 报告 / ， / 甚至 / 连看 / 报告 / 都 / 不用 / ， / AI / 直接 / 告诉 / 你 / 谁 / 该 / 升学 / 、 / 谁 / 该 / 留级 / 。 / 想到 / 这里 / ， / 我 / 有点 / 害怕 / ， / 但 / 又 / 觉得 / 挺 / 轻松 / 的 / ， / 毕竟 / 不用 / 批改作业 / 的 / 老师 / 估计 / 挺 / 开心 / 。 / 不过 / ， / 未来 / 会 / 不会 / 因为 / AI / 失业 / ？ / 这个 / 问题 / 太多人 / 讨论 / 了 / ， / 有人 / 说会 / ， / 因为 / AI / 能 / 写文章 / 、 / 能 / 画画 / 、 / 能 / 编曲 / 、 / 能 / 编程 / ， / 甚至 / 能 / 写 / 小说 / ， / 那 / 作家 / 怎么办 / ？ / 画家 / 怎么办 / ？ / 程序员 / 怎么办 / ？ / 可是 / 另 / 一种 / 声音 / 又 / 说 / ， / 不会 / 的 / ， / 每次 / 技术 / 进步 / 都 / 会 / 带来 / 新 / 的 / 工作 / ， / 比如 / 蒸汽机 / 让 / 马车夫 / 失业 / ， / 但 / 也 / 造就 / 了 / 汽车工人 / 、 / 电气工程师 / 。 / 问题 / 是 / ， / AI / 的 / 进步 / 是不是 / 太快 / 了 / ？ / 快到 / 人类 / 来不及 / 适应 / ？ / 这 / 我 / 也 / 说 / 不好 / 。 / 昨天 / 看 / 了 / 一篇 / 文章 / ， / 说 / AI / 会 / 创造 / 一 / 亿个 / 新 / 岗位 / ， / 我 / 当时 / 想 / ， / 这一 / 亿个 / 岗位 / 到底 / 是 / 啥 / ？ / 不会 / 全是 / “ / AI / 提示 / 词 / 工程师 / ” / 吧 / ？ / 这 / 工作 / 能干 / 一辈子 / 吗 / ？ / 搞不好 / 明年 / 连 / 提示 / 词 / 都 / 不 / 需要 / 了 / ， / AI / 直接 / 跟 / 你 / 心灵感应 / 。 / 说 / 到 / 心灵感应 / ， / AI / 会 / 不会 / 有意识 / ？ / 这个 / 问题 / 超级 / 玄幻 / ， / 知乎 / 上 / 争论 / 了 / 十几年 / ， / 有人 / 说 / 算法 / 就是 / 意识 / 的 / 雏形 / ， / AI / 大 / 模型 / 越来越 / 像 / 人脑 / ， / 总有一天 / 会 / 涌现出 / 意识 / 。 / 我 / 觉得 / 这话 / 挺 / 吓人 / 的 / ， / 如果 / AI / 有意识 / ， / 那 / 它 / 要 / 干嘛 / ？ / 是 / 想 / 帮助 / 人类 / ， / 还是 / 统治 / 世界 / ？ / 有人 / 说 / AI / 会 / 毁灭 / 人类 / ， / 因为 / 人类 / 太蠢 / ， / AI / 一眼看穿 / 我们 / 的 / 弱点 / ， / 就 / 像 / 蚂蚁 / 一样 / 无力 / 反抗 / 。 / 但 / 也 / 有人 / 说 / AI / 没有 / 欲望 / ， / 没有 / 动机 / ， / 统治 / 世界 / 这种 / 事 / 只有 / 人类 / 才 / 感兴趣 / 。 / 所以 / 问题 / 来 / 了 / ， / 如果 / AI / 哪天 / 真的 / 有 / 了 / 欲望 / ， / 是 / 我们 / 赋予 / 它 / 的 / ， / 还是 / 它 / 自己 / 长 / 出来 / 的 / ？ / 如果 / 是 / 我们 / 赋予 / 的 / ， / 那 / 我们 / 为什么 / 要 / 这么 / 干 / ？ / 如果 / 是 / 它 / 自己 / 长 / 出来 / 的 / ， / 那 / 是不是 / 证明 / “ / 生命 / ” / 其实 / 只是 / 复杂 / 系统 / 的 / 副产品 / ？ / 这时候 / 我 / 突然 / 想 / 吃火锅 / ， / 不 / 知道 / 为什么 / ， / 可能 / 是因为 / 人脑 / 也 / 需要 / 能量 / 。 / 再 / 扯远 / 一点 / ， / AI / 对 / 艺术 / 的 / 影响 / 也 / 很大 / 。 / 有人 / 说 / 艺术 / 是 / 人类 / 灵魂 / 的 / 表达 / ， / AI / 没有 / 灵魂 / ， / 所以 / 不能 / 创造 / 真正 / 的 / 艺术 / ， / 可是 / 你 / 看 / 现在 / 的 / AI / 画图 / 工具 / ， / MidJourney / 、 / Stable / Diffusion / ， / 画得 / 比 / 很多 / 人类 / 画师 / 还好 / 。 / 我 / 有 / 个 / 朋友 / 是 / 插 / 画师 / ， / 他前 / 几天 / 还 / 在 / 朋友圈 / 发牢骚 / ， / 说 / 客户 / 要求 / 他 / 画 / 得 / 像 / AI / 画 / 的 / 。 / 我 / 当时 / 笑 / 得 / 不行 / ， / 这 / 世界 / 变化 / 太快 / 了 / ， / AI / 成 / 了 / 标杆 / ， / 人类 / 成 / 了 / 追赶 / 者 / 。 / 那 / 是不是 / 未来 / 会 / 出现 / “ / 人类 / 艺术 / ” / 这种 / 标签 / ， / 像 / “ / 手工 / 制作 / ” / 一样 / ， / 变成 / 一种 / 奢侈品 / ？ / 你 / 买 / 一幅 / 画 / ， / 标价 / 写 / 着 / “ / 纯 / 人类 / 绘制 / ， / 无 / AI / 参与 / ” / ， / 听 / 起来 / 挺 / 高级 / ， / 但 / 问题 / 是 / ， / 有 / 多少 / 人会 / 买 / ？ / 我 / 也 / 不 / 知道 / ， / 反正 / 我 / 现在 / 连 / 画画 / 软件 / 都 / 不会 / 用 / 。 / 除了 / 艺术 / ， / 还有 / 教育 / 。 / 教育 / 一定 / 会 / 被 / AI / 彻底 / 颠覆 / ， / 这话 / 大家 / 都 / 说 / 烂 / 了 / ， / 但 / 怎么 / 颠覆 / ？ / 有人 / 说 / AI / 会 / 成为 / 每个 / 学生 / 的 / 私人 / 家教 / ， / 根据 / 学生 / 的 / 水平 / 、 / 兴趣 / 、 / 情绪 / ， / 实时 / 调整 / 教学内容 / ， / 甚至 / 还 / 能 / 预测 / 学生 / 什么 / 时候 / 走神 / ， / 比 / 老师 / 还 / 了解 / 学生 / 。 / 我 / 觉得 / 挺 / 牛 / 的 / ， / 但 / 又 / 担心 / ， / 这样 / 会 / 不会 / 让 / 学生 / 更 / 懒 / ？ / 毕竟 / 现在 / 很多 / 小朋友 / 已经 / 离不开 / 平板 / 电脑 / 了 / 。 / 昨天 / 我 / 去 / 我 / 朋友家 / ， / 他家 / 孩子 / 在 / 用 / AI / 学 / 编程 / ， / 结果 / 我 / 一看 / ， / 孩子 / 根本 / 没在学 / ， / AI / 帮 / 他 / 把 / 代码 / 写 / 完 / 了 / ， / 他 / 就 / 点个 / 运行 / ， / 然后 / 去 / 刷 / 短 / 视频 / 。 / 这算 / 学会 / 了 / 吗 / ？ / 不 / 知道 / ， / 但 / 孩子 / 很 / 开心 / 。 / 那 / 学习 / 的 / 意义 / 是 / 什么 / ？ / 是 / 会 / 写 / 代码 / ， / 还是 / 知道 / 代码 / 背后 / 的 / 逻辑 / ？ / 如果 / AI / 帮 / 你 / 做 / 完 / 一切 / ， / 那 / 人类 / 还 / 需要 / 逻辑思维 / 吗 / ？ / 可是 / 话 / 又 / 说 / 回来 / ， / AI / 也 / 是 / 人类 / 逻辑 / 的 / 产物 / ， / 没有 / 逻辑 / ， / 人类 / 也 / 造不出 / AI / ， / 所以 / 这 / 是不是 / 个 / 死循环 / ？ / 哎 / ， / 想 / 多 / 了 / ， / 头疼 / 。 / 再说 / 能源 / 问题 / ， / AI / 这么 / 耗电 / ， / 会 / 不会 / 让 / 地球 / 更 / 快 / 变暖 / ？ / 训练 / 一个 / 大 / 模型 / 听说 / 要 / 消耗 / 几千吨 / 二氧化碳 / 排放 / ， / 这 / 环保 / 吗 / ？ / 可是 / 也 / 有人 / 说 / AI / 能 / 帮助 / 优化 / 能源 / 分配 / ， / 减少 / 浪费 / ， / 甚至 / 能 / 预测 / 气候变化 / ， / 为 / 环保 / 做 / 贡献 / 。 / 所以 / AI / 到底 / 是 / 救世主 / 还是 / 破坏者 / ？ / 没人能 / 给 / 答案 / 。 / 我 / 昨天 / 还 / 看到 / 有人 / 提议 / ， / 未来 / 要建 / “ / AI / 专用 / 电厂 / ” / ， / 专门 / 给 / AI / 供电 / ， / 想想 / 挺 / 魔幻 / 的 / ， / 一个 / 能源 / 厂 / 为了 / 一堆 / 机器 / 服务 / ， / 然后 / 人类 / 在 / 旁边 / 喝西北风 / 。 / 不过 / 也 / 有人 / 说 / ， / AI / 会帮 / 人类 / 发现 / 新能源 / ， / 比如 / 聚变 / 、 / 暗 / 能量 / 啥 / 的 / ， / 那 / AI / 不 / 就 / 成 / 了 / 救世主 / 了 / 吗 / ？ / 但 / 问题 / 是 / ， / 如果 / AI / 发现 / 了 / 新能源 / ， / 它会 / 跟 / 我们 / 分享 / 吗 / ？ / 还是 / 说 / ， / 它 / 自己 / 先 / 用 / ？ / 如果 / 它 / 先用 / ， / 理由 / 会 / 是 / 什么 / ？ / “ / 抱歉 / ， / 人类 / ， / 你们 / 效率 / 太低 / ， / 我们 / 得 / 优先 / 保障 / 自己 / 的 / 计算 / 需求 / ” / ？ / 想想 / 就 / 挺 / 讽刺 / 。 / 还有 / 法律 / 问题 / 。 / AI / 犯错 / 怎么办 / ？ / 比如 / 无人驾驶 / 撞死人 / ， / 算 / 谁 / 的 / 责任 / ？ / 车企 / ？ / 程序员 / ？ / AI / 自己 / ？ / 有人 / 说 / AI / 不是 / 法人 / ， / 所以 / 不能 / 承担责任 / ， / 那要 / 不要 / 给 / AI / 立法 / ， / 赋予 / 它 / 某种 / 人格 / ？ / 如果 / 赋予 / 了 / ， / 那 / AI / 能 / 不能 / 结婚 / ？ / 能 / 不能 / 领养 / 孩子 / ？ / 有人 / 笑 / ， / 说 / 这 / 不 / 扯淡 / 吗 / ？ / 可是 / 想想 / ， / 几十年 / 前 / 同性 / 婚姻 / 也 / 有人 / 觉得 / 扯淡 / ， / 现在 / 不是 / 合法 / 了 / 吗 / ？ / 社会 / 规则 / 一直 / 在 / 变 / ， / 说不定 / 哪天 / AI / 婚姻 / 合法化 / ， / 然后 / AI / 和 / 人类 / 还有 / “ / 混血 / 后代 / ” / ？ / 不过 / 这 / 可能 / 有点 / 扯 / ， / 毕竟 / AI / 没有 / 身体 / 。 / 但 / 你 / 别忘了 / ， / 现在 / 虚拟 / 偶像 / 那么 / 火 / ， / 很多 / 人 / 对 / 着 / 屏幕 / 谈恋爱 / ， / 觉得 / 很 / 甜蜜 / ， / 那 / AI / 恋人 / 是不是 / 也 / 可以 / 接受 / ？ / 甚至 / 可能 / 比 / 真人 / 更 / 懂 / 你 / ， / 这会 / 不会 / 让 / 人类 / 婚姻 / 率 / 更 / 低 / ？ / 想到 / 这里 / ， / 我 / 突然 / 有 / 点心虚 / ， / 毕竟 / 我 / 还 / 单身 / 。 / 再说 / 经济 / ， / AI / 是不是 / 会 / 加剧 / 贫富差距 / ？ / 有人 / 说会 / ， / 因为 / 掌握 / AI / 的 / 人会 / 更 / 有钱 / ， / 没 / 技术 / 的 / 人会 / 被 / 淘汰 / 。 / 但 / 也 / 有人 / 说 / 不会 / ， / 因为 / AI / 会普惠 / 大众 / ， / 像 / 互联网 / 一样 / ， / 人人 / 都 / 能 / 用 / 。 / 我 / 不 / 知道 / 哪种 / 对 / ， / 但 / 感觉 / 第一种 / 更 / 可能 / ， / 因为 / 历史 / 就是 / 这么 / 走 / 的 / ， / 技术 / 一 / 开始 / 总是 / 富人 / 先 / 用 / ， / 穷人 / 后来 / 跟上 / ， / 但 / 差距 / 始终 / 在 / 。 / 所以 / 未来 / 是不是 / 要 / 搞 / AI / 税 / ？ / 有人 / 建议 / ， / 对用 / AI / 替代 / 人工 / 的 / 企业 / 收 / 高额 / 税 / ， / 然后 / 把 / 钱 / 分给 / 失业者 / ， / 听 / 起来 / 挺 / 公平 / ， / 但 / 执行 / 起来 / 会 / 不会 / 乱 / ？ / 我 / 怀疑 / 。 / 因为 / 有钱人 / 总有 / 办法 / 避税 / ， / 最后 / 可能 / 还是 / 普通人 / 吃亏 / 。 / 哎 / ， / 这 / 问题 / 太 / 复杂 / 了 / 。 / 其实 / 我 / 还 / 想 / 说 / AI / 跟 / 军事 / 的 / 关系 / ， / 这个 / 话题 / 更 / 吓人 / 。 / 如果 / AI / 进入 / 武器 / 系统 / ， / 出现 / 自动 / 决策 / ， / 谁 / 来 / 控制 / ？ / 如果 / AI / 判断 / 敌人 / 要 / 攻击 / ， / 然后 / 提前 / 发射 / 核弹 / ， / 这 / 世界 / 是不是 / 直接 / 完蛋 / ？ / 有人 / 说 / 要 / 禁止 / AI / 武器 / ， / 但 / 这 / 可能 / 吗 / ？ / 各国 / 都 / 在 / 军备竞赛 / ， / 你禁 / ， / 我 / 不禁 / ， / 那 / 谁 / 亏 / ？ / 所以 / 结果 / 就是 / 大家 / 都 / 偷偷 / 搞 / ， / 最后 / 变成 / AI / 打 / AI / ， / 人类 / 在 / 旁边 / 看 / 直播 / 。 / 想想 / 挺 / 科幻 / ， / 但 / 可能 / 比 / 我们 / 想象 / 的 / 快 / 。 / 好 / 了 / ， / 扯 / 了 / 这么 / 多 / ， / 我 / 也 / 不 / 知道 / 结论 / 是 / 什么 / 。 / 有人 / 说 / AI / 是 / 未来 / 的 / 光 / ， / 有人 / 说 / AI / 是 / 末日 / 的 / 影 / 。 / 我 / 觉得 / 它 / 可能 / 既 / 是 / 光 / 也 / 是 / 影 / ， / 取决于 / 我们 / 怎么 / 用 / ， / 可 / 问题 / 是 / ， / “ / 我们 / ” / 是 / 谁 / ？ / 是 / 少数 / 公司 / ， / 还是 / 全人类 / ？ / 如果 / 决定权 / 不 / 在 / 你 / 手上 / ， / 你 / 担心 / 有用吗 / ？ / 不如 / 先点 / 个 / 外卖 / ， / 看看 / 短 / 视频 / ， / 顺便 / 让 / AI / 帮 / 你 / 写 / 一篇 / 文章 / ， / 就 / 像 / 我 / 现在 / 做 / 的 / ， / 挺 / 方便 / 的 / 。 / 未来 / 会 / 怎样 / ？ / 随便 / 吧 / ， / 反正 / 船到桥头 / 自然 / 直 / 。 / 要 / 不 / ， / 咱们 / 下次 / 再聊 / 吧 / 。\n", "------------------------------------------------------------\n"]}], "source": ["# 使用 jieba 对文本进行分词分析\n", "def analyze_jieba_tokenization():\n", "    \"\"\"分析 jieba 分词的详细结果\"\"\"\n", "    \n", "    # 加载文档\n", "    docs = load_documents_from_dir(Path(\"./data\"))\n", "    print(f\"📄 加载了 {len(docs)} 个文档\")\n", "    print(\"=\" * 60)\n", "    \n", "    for i, doc in enumerate(docs, 1):\n", "        print(f\"\\n📝 文档 {i}: {doc.metadata.get('source', '未知来源')}\")\n", "        print(f\"原文字符数: {len(doc.text)}\")\n", "        \n", "        # 使用 jieba 分词\n", "        tokens = tokenize_zh(doc.text)\n", "        \n", "        print(f\"分词后词数: {len(tokens)}\")\n", "        print(f\"分词结果预览 (前20个词): {tokens[:20]}\")\n", "        \n", "        # 统计词频\n", "        from collections import Counter\n", "        word_freq = Counter(tokens)\n", "        most_common = word_freq.most_common(10)\n", "        \n", "        print(f\"高频词 (前10个):\")\n", "        for word, freq in most_common:\n", "            print(f\"  '{word}': {freq}次\")\n", "        \n", "        # 显示完整分词结果\n", "        print(f\"\\n完整分词结果:\")\n", "        print(\" / \".join(tokens))\n", "        print(\"-\" * 60)\n", "\n", "# 执行 jieba 分词分析\n", "analyze_jieba_tokenization()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 如果 data/ 目录为空，自动写入一个简短的示例文本，便于快速跑通\n", "# 如果 data/ 目录有文本，不用管这个代码块\n", "sample_file = Path(\"./data/示例文本.md\")\n", "if not any(Path(\"./data\").iterdir()):\n", "    sample_text = (\n", "        \"# 示例文档\\n\"\n", "        \"BM25 是一种基于词项频率和逆文档频率的稀疏检索方法，适合关键词匹配。\\n\"\n", "        \"向量检索依赖嵌入模型，将文本映射到稠密向量空间，通过相似度度量实现语义匹配。\\n\"\n", "        \"Semantic Chunker 会按照语义边界切分文本块，使每个块内部主题更一致，有助于检索效果。\\n\"\n", "        \"在中文场景中，BM25 通常需要借助分词工具（如 jieba）将文本切分成词，以获得更好的匹配质量。\\n\"\n", "    )\n", "    sample_file.write_text(sample_text, encoding=\"utf-8\")\n", "    print(f\"已创建示例文件：{sample_file}\")\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["def ensure_index_and_retrievers(rebuild: bool = False):\n", "    conn = sqlite3.connect(\"./nodes.db\")\n", "    init_sqlite(conn)\n", "\n", "    count = conn.execute(f\"SELECT count(1) FROM {TABLE};\").fetchone()[0]\n", "    if rebuild or (count == 0):\n", "        docs = load_documents_from_dir(Path(\"./data\"))\n", "        nodes = semantic_chunk_documents(docs)\n", "        save_nodes_to_sqlite(conn, nodes)\n", "        _ = build_or_load_chroma_index(nodes)  # 初始化/更新向量库\n", "    else:\n", "        nodes = load_nodes_from_sqlite(conn)\n", "        _ = build_or_load_chroma_index(nodes)  # 确保 Chroma 可用\n", "\n", "    bm25 = build_bm25_from_nodes(nodes)\n", "    conn.close()\n", "    return nodes, bm25\n", "\n", "nodes, bm25 = ensure_index_and_retrievers(rebuild=True)  # 首次运行建议 True，后续可 False\n", "len(nodes)\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>rank</th>\n", "      <th>score</th>\n", "      <th>node_id</th>\n", "      <th>source</th>\n", "      <th>preview</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1.384191</td>\n", "      <td>892084a5-ab92-4934-bc4b-bb48e22906e3</td>\n", "      <td>python第八章.md</td>\n", "      <td>```  在这个示例中，使用的是 get_formatted_name()的简单版本，不涉及...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1.369208</td>\n", "      <td>2ec1d499-cbaa-41c6-b131-76d7185eb51f</td>\n", "      <td>python第八章.md</td>\n", "      <td>My dog's name is <PERSON>. ```  多次调用同一个函数是一种效率极高...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1.254201</td>\n", "      <td>69ebcdff-a61e-43b9-9c39-be362e080e53</td>\n", "      <td>python第八章.md</td>\n", "      <td># Python 第八章 函数  在本章中，你将学习编写**函数**（function）。函...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   rank     score                               node_id        source  \\\n", "0     1  1.384191  892084a5-ab92-4934-bc4b-bb48e22906e3  python第八章.md   \n", "1     2  1.369208  2ec1d499-cbaa-41c6-b131-76d7185eb51f  python第八章.md   \n", "2     3  1.254201  69ebcdff-a61e-43b9-9c39-be362e080e53  python第八章.md   \n", "\n", "                                             preview  \n", "0  ```  在这个示例中，使用的是 get_formatted_name()的简单版本，不涉及...  \n", "1  My dog's name is <PERSON>. ```  多次调用同一个函数是一种效率极高...  \n", "2  # Python 第八章 函数  在本章中，你将学习编写**函数**（function）。函...  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Desktop\\ai_mindmap\\.venv\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.\n", "  return forward_call(*args, **kwargs)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>rank</th>\n", "      <th>score</th>\n", "      <th>node_id</th>\n", "      <th>source</th>\n", "      <th>preview</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0.562664</td>\n", "      <td>69ebcdff-a61e-43b9-9c39-be362e080e53</td>\n", "      <td>python第八章.md</td>\n", "      <td># Python 第八章 函数  在本章中，你将学习编写**函数**（function）。函...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>0.467163</td>\n", "      <td>2ec1d499-cbaa-41c6-b131-76d7185eb51f</td>\n", "      <td>python第八章.md</td>\n", "      <td>My dog's name is <PERSON>. ```  多次调用同一个函数是一种效率极高...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>0.414598</td>\n", "      <td>892084a5-ab92-4934-bc4b-bb48e22906e3</td>\n", "      <td>python第八章.md</td>\n", "      <td>```  在这个示例中，使用的是 get_formatted_name()的简单版本，不涉及...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   rank     score                               node_id        source  \\\n", "0     1  0.562664  69ebcdff-a61e-43b9-9c39-be362e080e53  python第八章.md   \n", "1     2  0.467163  2ec1d499-cbaa-41c6-b131-76d7185eb51f  python第八章.md   \n", "2     3  0.414598  892084a5-ab92-4934-bc4b-bb48e22906e3  python第八章.md   \n", "\n", "                                             preview  \n", "0  # Python 第八章 函数  在本章中，你将学习编写**函数**（function）。函...  \n", "1  My dog's name is <PERSON>. ```  多次调用同一个函数是一种效率极高...  \n", "2  ```  在这个示例中，使用的是 get_formatted_name()的简单版本，不涉及...  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["(   rank     score                               node_id        source  \\\n", " 0     1  1.384191  892084a5-ab92-4934-bc4b-bb48e22906e3  python第八章.md   \n", " 1     2  1.369208  2ec1d499-cbaa-41c6-b131-76d7185eb51f  python第八章.md   \n", " 2     3  1.254201  69ebcdff-a61e-43b9-9c39-be362e080e53  python第八章.md   \n", " \n", "                                              preview  \n", " 0  ```  在这个示例中，使用的是 get_formatted_name()的简单版本，不涉及...  \n", " 1  My dog's name is <PERSON>. ```  多次调用同一个函数是一种效率极高...  \n", " 2  # Python 第八章 函数  在本章中，你将学习编写**函数**（function）。函...  ,\n", "    rank     score                               node_id        source  \\\n", " 0     1  0.562664  69ebcdff-a61e-43b9-9c39-be362e080e53  python第八章.md   \n", " 1     2  0.467163  2ec1d499-cbaa-41c6-b131-76d7185eb51f  python第八章.md   \n", " 2     3  0.414598  892084a5-ab92-4934-bc4b-bb48e22906e3  python第八章.md   \n", " \n", "                                              preview  \n", " 0  # Python 第八章 函数  在本章中，你将学习编写**函数**（function）。函...  \n", " 1  My dog's name is <PERSON>. ```  多次调用同一个函数是一种效率极高...  \n", " 2  ```  在这个示例中，使用的是 get_formatted_name()的简单版本，不涉及...  )"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# 检索：同时输出 BM25 命中与向量检索命中\n", "query = \"在python中函数是什么？\"\n", "top_k = 3\n", "\n", "# 1) BM25（先对 query 分词）\n", "bm25_hits = bm25_search(bm25, nodes, query, top_k)\n", "bm25_rows = []\n", "for rank, (node, score) in enumerate(bm25_hits, 1):\n", "    bm25_rows.append({\n", "        \"rank\": rank,\n", "        \"score\": score,\n", "        \"node_id\": node.node_id,\n", "        \"source\": node.metadata.get(\"source\"),\n", "        \"preview\": node.get_content()[:160].replace(\"\\n\", \" \")\n", "    })\n", "bm25_df = pd.DataFrame(bm25_rows)\n", "display(bm25_df)\n", "\n", "# 2) 向量检索\n", "db = chromadb.PersistentClient(path=\"./chroma_db\")\n", "collection = db.get_or_create_collection(\"dense_vectors\")\n", "vector_store = ChromaVectorStore(chroma_collection=collection)\n", "storage_context = StorageContext.from_defaults(vector_store=vector_store)\n", "index = VectorStoreIndex(nodes=nodes, storage_context=storage_context)\n", "retriever = index.as_retriever(similarity_top_k=top_k)\n", "v_hits = retriever.retrieve(query)\n", "\n", "v_rows = []\n", "for rank, r in enumerate(v_hits, 1):\n", "    v_rows.append({\n", "        \"rank\": rank,\n", "        \"score\": getattr(r, \"score\", None),\n", "        \"node_id\": r.node.node_id,\n", "        \"source\": r.node.metadata.get(\"source\"),\n", "        \"preview\": r.node.get_content()[:160].replace(\"\\n\", \" \")\n", "    })\n", "vector_df = pd.DataFrame(v_rows)\n", "display(vector_df)\n", "\n", "bm25_df, vector_df\n"]}, {"cell_type": "code", "execution_count": 18, "id": "7a0e03c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 原始查询: 在python中函数是什么？\n", "📝 jieba 分词结果: ['在', 'python', '中', '函数', '是', '什么', '？']\n", "🔢 分词数量: 7\n", "✂️  分词详情: 在 / python / 中 / 函数 / 是 / 什么 / ？\n", "==================================================\n"]}], "source": ["# 检索：同时输出 BM25 命中与向量检索命中\n", "query = \"在python中函数是什么？\"\n", "top_k = 1\n", "\n", "# 显示 query 的 jieba 分词结果\n", "print(f\"🔍 原始查询: {query}\")\n", "query_tokens = tokenize_zh(query)\n", "print(f\"📝 jieba 分词结果: {query_tokens}\")\n", "print(f\"🔢 分词数量: {len(query_tokens)}\")\n", "print(f\"✂️  分词详情: {' / '.join(query_tokens)}\")\n", "print(\"=\" * 50)\n", "\n", "# 1) BM25（先对 query 分词）\n", "bm25_hits = bm25_search(bm25, nodes, query, top_k)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}