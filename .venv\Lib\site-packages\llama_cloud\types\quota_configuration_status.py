# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class QuotaConfigurationStatus(str, enum.Enum):
    """
    The status of the quota, i.e. 'ACTIVE' or 'INACTIVE'
    """

    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"

    def visit(self, active: typing.Callable[[], T_Result], inactive: typing.Callable[[], T_Result]) -> T_Result:
        if self is QuotaConfigurationStatus.ACTIVE:
            return active()
        if self is QuotaConfigurationStatus.INACTIVE:
            return inactive()
