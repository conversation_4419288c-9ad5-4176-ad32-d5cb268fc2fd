# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .configurable_data_source_names import ConfigurableDataSourceNames
from .data_source_reader_version_metadata import DataSourceReaderVersionMetadata
from .pipeline_data_source_component import PipelineDataSourceComponent
from .pipeline_data_source_custom_metadata_value import PipelineDataSourceCustomMetadataValue
from .pipeline_data_source_status import PipelineDataSourceStatus

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class PipelineDataSource(pydantic.BaseModel):
    """
    Schema for a data source in a pipeline.
    """

    id: str = pydantic.Field(description="Unique identifier")
    created_at: typing.Optional[dt.datetime]
    updated_at: typing.Optional[dt.datetime]
    name: str = pydantic.Field(description="The name of the data source.")
    source_type: ConfigurableDataSourceNames
    custom_metadata: typing.Optional[typing.Dict[str, typing.Optional[PipelineDataSourceCustomMetadataValue]]]
    component: PipelineDataSourceComponent = pydantic.Field(description="Component that implements the data source")
    version_metadata: typing.Optional[DataSourceReaderVersionMetadata]
    project_id: str
    data_source_id: str = pydantic.Field(description="The ID of the data source.")
    pipeline_id: str = pydantic.Field(description="The ID of the pipeline.")
    last_synced_at: dt.datetime = pydantic.Field(description="The last time the data source was automatically synced.")
    sync_interval: typing.Optional[float]
    sync_schedule_set_by: typing.Optional[str]
    status: typing.Optional[PipelineDataSourceStatus]
    status_updated_at: typing.Optional[dt.datetime]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
