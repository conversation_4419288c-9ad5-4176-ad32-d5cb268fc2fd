# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .fail_page_mode import FailPageMode
from .llama_parse_parameters_priority import LlamaParseParametersPriority
from .parser_languages import ParserLanguages
from .parsing_mode import ParsingMode
from .webhook_configuration import WebhookConfiguration

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class LlamaParseParameters(pydantic.BaseModel):
    """
    Settings that can be configured for how to use LlamaParse to parse files within a LlamaCloud pipeline.
    """

    webhook_configurations: typing.Optional[typing.List[WebhookConfiguration]]
    priority: typing.Optional[LlamaParseParametersPriority]
    languages: typing.Optional[typing.List[ParserLanguages]]
    parsing_instruction: typing.Optional[str]
    disable_ocr: typing.Optional[bool]
    annotate_links: typing.Optional[bool]
    adaptive_long_table: typing.Optional[bool]
    compact_markdown_table: typing.Optional[bool]
    disable_reconstruction: typing.Optional[bool]
    disable_image_extraction: typing.Optional[bool]
    invalidate_cache: typing.Optional[bool]
    outlined_table_extraction: typing.Optional[bool]
    merge_tables_across_pages_in_markdown: typing.Optional[bool]
    output_pdf_of_document: typing.Optional[bool]
    do_not_cache: typing.Optional[bool]
    fast_mode: typing.Optional[bool]
    skip_diagonal_text: typing.Optional[bool]
    preserve_layout_alignment_across_pages: typing.Optional[bool]
    preserve_very_small_text: typing.Optional[bool]
    gpt_4_o_mode: typing.Optional[bool] = pydantic.Field(alias="gpt4o_mode")
    gpt_4_o_api_key: typing.Optional[str] = pydantic.Field(alias="gpt4o_api_key")
    do_not_unroll_columns: typing.Optional[bool]
    extract_layout: typing.Optional[bool]
    high_res_ocr: typing.Optional[bool]
    html_make_all_elements_visible: typing.Optional[bool]
    html_remove_navigation_elements: typing.Optional[bool]
    html_remove_fixed_elements: typing.Optional[bool]
    guess_xlsx_sheet_name: typing.Optional[bool]
    page_separator: typing.Optional[str]
    bounding_box: typing.Optional[str]
    bbox_top: typing.Optional[float]
    bbox_right: typing.Optional[float]
    bbox_bottom: typing.Optional[float]
    bbox_left: typing.Optional[float]
    target_pages: typing.Optional[str]
    use_vendor_multimodal_model: typing.Optional[bool]
    vendor_multimodal_model_name: typing.Optional[str]
    model: typing.Optional[str]
    vendor_multimodal_api_key: typing.Optional[str]
    page_prefix: typing.Optional[str]
    page_suffix: typing.Optional[str]
    webhook_url: typing.Optional[str]
    preset: typing.Optional[str]
    take_screenshot: typing.Optional[bool]
    is_formatting_instruction: typing.Optional[bool]
    premium_mode: typing.Optional[bool]
    continuous_mode: typing.Optional[bool]
    input_s_3_path: typing.Optional[str] = pydantic.Field(alias="input_s3_path")
    input_s_3_region: typing.Optional[str] = pydantic.Field(alias="input_s3_region")
    output_s_3_path_prefix: typing.Optional[str] = pydantic.Field(alias="output_s3_path_prefix")
    output_s_3_region: typing.Optional[str] = pydantic.Field(alias="output_s3_region")
    project_id: typing.Optional[str]
    azure_openai_deployment_name: typing.Optional[str]
    azure_openai_endpoint: typing.Optional[str]
    azure_openai_api_version: typing.Optional[str]
    azure_openai_key: typing.Optional[str]
    input_url: typing.Optional[str]
    http_proxy: typing.Optional[str]
    auto_mode: typing.Optional[bool]
    auto_mode_trigger_on_regexp_in_page: typing.Optional[str]
    auto_mode_trigger_on_text_in_page: typing.Optional[str]
    auto_mode_trigger_on_table_in_page: typing.Optional[bool]
    auto_mode_trigger_on_image_in_page: typing.Optional[bool]
    auto_mode_configuration_json: typing.Optional[str]
    structured_output: typing.Optional[bool]
    structured_output_json_schema: typing.Optional[str]
    structured_output_json_schema_name: typing.Optional[str]
    max_pages: typing.Optional[int]
    max_pages_enforced: typing.Optional[int]
    extract_charts: typing.Optional[bool]
    formatting_instruction: typing.Optional[str]
    complemental_formatting_instruction: typing.Optional[str]
    content_guideline_instruction: typing.Optional[str]
    spreadsheet_extract_sub_tables: typing.Optional[bool]
    job_timeout_in_seconds: typing.Optional[float]
    job_timeout_extra_time_per_page_in_seconds: typing.Optional[float]
    strict_mode_image_extraction: typing.Optional[bool]
    strict_mode_image_ocr: typing.Optional[bool]
    strict_mode_reconstruction: typing.Optional[bool]
    strict_mode_buggy_font: typing.Optional[bool]
    save_images: typing.Optional[bool]
    hide_headers: typing.Optional[bool]
    hide_footers: typing.Optional[bool]
    page_header_prefix: typing.Optional[str]
    page_header_suffix: typing.Optional[str]
    page_footer_prefix: typing.Optional[str]
    page_footer_suffix: typing.Optional[str]
    ignore_document_elements_for_layout_detection: typing.Optional[bool]
    output_tables_as_html: typing.Optional[bool] = pydantic.Field(alias="output_tables_as_HTML")
    internal_is_screenshot_job: typing.Optional[bool]
    parse_mode: typing.Optional[ParsingMode]
    system_prompt: typing.Optional[str]
    system_prompt_append: typing.Optional[str]
    user_prompt: typing.Optional[str]
    page_error_tolerance: typing.Optional[float]
    replace_failed_page_mode: typing.Optional[FailPageMode]
    replace_failed_page_with_error_message_prefix: typing.Optional[str]
    replace_failed_page_with_error_message_suffix: typing.Optional[str]
    markdown_table_multiline_header_separator: typing.Optional[str]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        json_encoders = {dt.datetime: serialize_datetime}
