bm25s-0.2.13.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
bm25s-0.2.13.dist-info/LICENSE,sha256=jBWbTGIahsRpXIP0KgCTKyYYP22oq2Kwhlg1WUQmyP8,1068
bm25s-0.2.13.dist-info/METADATA,sha256=HXiYJGS3HM0UmhJL4gvhjpsTfaviBSEecdXDVhqqn_o,21740
bm25s-0.2.13.dist-info/RECORD,,
bm25s-0.2.13.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bm25s-0.2.13.dist-info/WHEEL,sha256=tZoeGjtWxWRfdplE7E3d45VPlLNQnvbKiYnx7gwAy8A,92
bm25s-0.2.13.dist-info/top_level.txt,sha256=mdzd-E617kgQPuW4FVDTIWvGKR0aBvG_rgevIbwjTL4,6
bm25s/__init__.py,sha256=O7jemYKETD6RizD1hDqwCigntTG-W5CpxpanQbdJBHk,46902
bm25s/__pycache__/__init__.cpython-311.pyc,,
bm25s/__pycache__/hf.cpython-311.pyc,,
bm25s/__pycache__/scoring.cpython-311.pyc,,
bm25s/__pycache__/selection.cpython-311.pyc,,
bm25s/__pycache__/stopwords.cpython-311.pyc,,
bm25s/__pycache__/tokenization.cpython-311.pyc,,
bm25s/__pycache__/version.cpython-311.pyc,,
bm25s/hf.py,sha256=rx7kwCvXiLE6MFc5Cxf-8AJ5OuEQ2-Y-NeEAnLupKKs,18800
bm25s/numba/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bm25s/numba/__pycache__/__init__.cpython-311.pyc,,
bm25s/numba/__pycache__/retrieve_utils.cpython-311.pyc,,
bm25s/numba/__pycache__/selection.cpython-311.pyc,,
bm25s/numba/retrieve_utils.py,sha256=8zOk00jZbUc5kVwKLr8tSAwUMprfi3Nm1xru1MVauLg,5146
bm25s/numba/selection.py,sha256=aZddfqY6881AcgexiDvTL2lTAb_P5GhJslrGiHsuq08,4514
bm25s/scoring.py,sha256=75zdRHOkIj5fJbA-btuxa2aM5y0qoNTJldnp0hBevTg,12077
bm25s/selection.py,sha256=liGtDilpcMJ3EuEbD5He_LMXrLBnNL_UMxpnifrbz_g,2497
bm25s/stopwords.py,sha256=zSo3rOC4ndIGkPsmAj5ykYI8vDhLKrhK6GNRDEHNguI,36736
bm25s/tokenization.py,sha256=4xesk86oFMnGqyu-duVhFTtwCzTJL8iFUQs0mCL1yDQ,28730
bm25s/utils/__init__.py,sha256=EZ_SMXLrxYGGqaUsiSmqwzh782H9fX1SXTSOZapFmGU,53
bm25s/utils/__pycache__/__init__.cpython-311.pyc,,
bm25s/utils/__pycache__/beir.cpython-311.pyc,,
bm25s/utils/__pycache__/benchmark.cpython-311.pyc,,
bm25s/utils/__pycache__/corpus.cpython-311.pyc,,
bm25s/utils/__pycache__/json_functions.cpython-311.pyc,,
bm25s/utils/beir.py,sha256=HEXpdDs3HmcZ-FeI4lTJJjny9Ny4ua1y5b6UxaiDPlc,11574
bm25s/utils/benchmark.py,sha256=dBgBJiB8b9m9IoxNIx-plmv-WJOMDzzuCkSKluJPOHk,3928
bm25s/utils/corpus.py,sha256=wnQRnhm1XrVCu1bNioGo9XSiBtva9Klg50W-qo2KzIs,6692
bm25s/utils/json_functions.py,sha256=jD6_we_iAko2aScp0e6mqMSWdpZrls5MNH-9wzgDRFA,812
bm25s/version.py,sha256=F6FMO2VYZ6XPnMD-zJ-CJVp-2OHhDohcWS03N6bpokA,22
