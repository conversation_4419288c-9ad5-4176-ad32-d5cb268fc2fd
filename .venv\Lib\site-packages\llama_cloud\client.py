# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import httpx

from .core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from .environment import LlamaCloudEnvironment
from .resources.admin.client import AdminClient, AsyncAdminClient
from .resources.agent_deployments.client import AgentDeploymentsClient, AsyncAgentDeploymentsClient
from .resources.beta.client import AsyncBetaClient, BetaClient
from .resources.chat_apps.client import AsyncChatAppsClient, ChatAppsClient
from .resources.classifier.client import AsyncClassifierClient, ClassifierClient
from .resources.data_sinks.client import AsyncDataSinksClient, DataSinksClient
from .resources.data_sources.client import AsyncDataSourcesClient, DataSourcesClient
from .resources.embedding_model_configs.client import AsyncEmbeddingModelConfigsClient, EmbeddingModelConfigsClient
from .resources.evals.client import AsyncEvalsClient, EvalsClient
from .resources.files.client import AsyncFilesClient, FilesClient
from .resources.jobs.client import AsyncJobs<PERSON>lient, JobsClient
from .resources.llama_extract.client import Async<PERSON>lamaExtractClient, LlamaExtractClient
from .resources.organizations.client import AsyncOrganizationsClient, OrganizationsClient
from .resources.parsing.client import AsyncParsingClient, ParsingClient
from .resources.pipelines.client import AsyncPipelinesClient, PipelinesClient
from .resources.projects.client import AsyncProjectsClient, ProjectsClient
from .resources.reports.client import AsyncReportsClient, ReportsClient
from .resources.retrievers.client import AsyncRetrieversClient, RetrieversClient
from .resources.users.client import AsyncUsersClient, UsersClient


class LlamaCloud:
    def __init__(
        self,
        *,
        base_url: typing.Optional[str] = None,
        environment: LlamaCloudEnvironment = LlamaCloudEnvironment.DEFAULT,
        token: typing.Optional[typing.Union[str, typing.Callable[[], str]]] = None,
        timeout: typing.Optional[float] = 60,
        httpx_client: typing.Optional[httpx.Client] = None
    ):
        self._client_wrapper = SyncClientWrapper(
            base_url=_get_base_url(base_url=base_url, environment=environment),
            token=token,
            httpx_client=httpx.Client(timeout=timeout) if httpx_client is None else httpx_client,
        )
        self.data_sinks = DataSinksClient(client_wrapper=self._client_wrapper)
        self.data_sources = DataSourcesClient(client_wrapper=self._client_wrapper)
        self.embedding_model_configs = EmbeddingModelConfigsClient(client_wrapper=self._client_wrapper)
        self.organizations = OrganizationsClient(client_wrapper=self._client_wrapper)
        self.projects = ProjectsClient(client_wrapper=self._client_wrapper)
        self.files = FilesClient(client_wrapper=self._client_wrapper)
        self.pipelines = PipelinesClient(client_wrapper=self._client_wrapper)
        self.retrievers = RetrieversClient(client_wrapper=self._client_wrapper)
        self.jobs = JobsClient(client_wrapper=self._client_wrapper)
        self.evals = EvalsClient(client_wrapper=self._client_wrapper)
        self.parsing = ParsingClient(client_wrapper=self._client_wrapper)
        self.chat_apps = ChatAppsClient(client_wrapper=self._client_wrapper)
        self.agent_deployments = AgentDeploymentsClient(client_wrapper=self._client_wrapper)
        self.classifier = ClassifierClient(client_wrapper=self._client_wrapper)
        self.admin = AdminClient(client_wrapper=self._client_wrapper)
        self.users = UsersClient(client_wrapper=self._client_wrapper)
        self.llama_extract = LlamaExtractClient(client_wrapper=self._client_wrapper)
        self.reports = ReportsClient(client_wrapper=self._client_wrapper)
        self.beta = BetaClient(client_wrapper=self._client_wrapper)


class AsyncLlamaCloud:
    def __init__(
        self,
        *,
        base_url: typing.Optional[str] = None,
        environment: LlamaCloudEnvironment = LlamaCloudEnvironment.DEFAULT,
        token: typing.Optional[typing.Union[str, typing.Callable[[], str]]] = None,
        timeout: typing.Optional[float] = 60,
        httpx_client: typing.Optional[httpx.AsyncClient] = None
    ):
        self._client_wrapper = AsyncClientWrapper(
            base_url=_get_base_url(base_url=base_url, environment=environment),
            token=token,
            httpx_client=httpx.AsyncClient(timeout=timeout) if httpx_client is None else httpx_client,
        )
        self.data_sinks = AsyncDataSinksClient(client_wrapper=self._client_wrapper)
        self.data_sources = AsyncDataSourcesClient(client_wrapper=self._client_wrapper)
        self.embedding_model_configs = AsyncEmbeddingModelConfigsClient(client_wrapper=self._client_wrapper)
        self.organizations = AsyncOrganizationsClient(client_wrapper=self._client_wrapper)
        self.projects = AsyncProjectsClient(client_wrapper=self._client_wrapper)
        self.files = AsyncFilesClient(client_wrapper=self._client_wrapper)
        self.pipelines = AsyncPipelinesClient(client_wrapper=self._client_wrapper)
        self.retrievers = AsyncRetrieversClient(client_wrapper=self._client_wrapper)
        self.jobs = AsyncJobsClient(client_wrapper=self._client_wrapper)
        self.evals = AsyncEvalsClient(client_wrapper=self._client_wrapper)
        self.parsing = AsyncParsingClient(client_wrapper=self._client_wrapper)
        self.chat_apps = AsyncChatAppsClient(client_wrapper=self._client_wrapper)
        self.agent_deployments = AsyncAgentDeploymentsClient(client_wrapper=self._client_wrapper)
        self.classifier = AsyncClassifierClient(client_wrapper=self._client_wrapper)
        self.admin = AsyncAdminClient(client_wrapper=self._client_wrapper)
        self.users = AsyncUsersClient(client_wrapper=self._client_wrapper)
        self.llama_extract = AsyncLlamaExtractClient(client_wrapper=self._client_wrapper)
        self.reports = AsyncReportsClient(client_wrapper=self._client_wrapper)
        self.beta = AsyncBetaClient(client_wrapper=self._client_wrapper)


def _get_base_url(*, base_url: typing.Optional[str] = None, environment: LlamaCloudEnvironment) -> str:
    if base_url is not None:
        return base_url
    elif environment is not None:
        return environment.value
    else:
        raise Exception("Please pass in either base_url or environment to construct the client")
